# AI服务节点

本目录包含了DL引擎可视化脚本系统中的AI服务节点实现。这些节点提供了完整的AI功能，包括模型管理、推理、训练、自然语言处理、计算机视觉等。

## 节点列表

### 核心AI节点
1. **AIModelLoadNode** - AI模型加载节点
   - 支持多种框架（TensorFlow、PyTorch、ONNX等）
   - 支持多种设备类型（CPU、GPU等）
   - 提供模型加载状态和性能指标

2. **AIInferenceNode** - AI推理节点
   - 支持批量推理
   - 可配置超时和优先级
   - 返回推理结果和置信度

3. **AITrainingNode** - AI训练节点
   - 支持模型训练和验证
   - 可配置超参数
   - 提供训练指标和模型保存

### 专业AI节点
4. **NLPProcessingNode** - 自然语言处理节点
   - 支持分词、词性标注、命名实体识别
   - 支持情感分析和文本摘要
   - 多语言支持

5. **ComputerVisionNode** - 计算机视觉节点
   - 支持图像分类、目标检测、图像分割
   - 支持OCR文字识别
   - 支持人脸检测

6. **SpeechRecognitionNode** - 语音识别节点
   - 支持语音转文字
   - 支持词级别时间戳
   - 多语言和采样率支持

7. **SentimentAnalysisNode** - 情感分析节点
   - 支持文档级和句子级情感分析
   - 支持方面级情感分析
   - 提供详细的情感得分

8. **RecommendationNode** - 推荐系统节点
   - 支持协同过滤和内容推荐
   - 支持用户画像和上下文推荐
   - 提供推荐解释

9. **ChatbotNode** - 聊天机器人节点
   - 支持多轮对话
   - 支持意图识别和实体提取
   - 支持个性化回复

### 管理和优化节点
10. **AIOptimizationNode** - AI优化节点
    - 支持模型量化和压缩
    - 支持多平台优化
    - 提供性能提升统计

11. **AIMonitoringNode** - AI监控节点
    - 实时监控模型性能
    - 提供告警和趋势分析
    - 支持健康状态检查

12. **AIModelVersionNode** - AI模型版本节点
    - 支持模型版本管理
    - 支持版本部署和回滚
    - 支持版本比较

13. **AIDataPreprocessingNode** - AI数据预处理节点
    - 支持多种数据预处理操作
    - 提供数据统计和变换信息
    - 支持不同数据类型

14. **AIResultPostprocessingNode** - AI结果后处理节点
    - 支持推理结果后处理
    - 支持多种输出格式
    - 提供置信度和元数据

15. **AIPerformanceNode** - AI性能监控节点
    - 监控系统性能指标
    - 识别性能瓶颈
    - 提供优化建议

## 技术特性

### 统一的节点架构
- 所有节点继承自`AIServiceNode`基类
- 统一的输入输出接口
- 标准化的错误处理

### 灵活的配置
- 支持多种AI框架和模型格式
- 可配置的参数和选项
- 动态的服务发现和绑定

### 高性能设计
- 异步处理支持
- 批量操作优化
- 内存和计算资源管理

### 易于扩展
- 模块化的服务架构
- 插件式的功能扩展
- 标准化的接口定义

## 使用示例

```typescript
// 创建AI模型加载节点
const modelLoadNode = new AIModelLoadNode();
const loadResult = modelLoadNode.execute({
  modelId: 'my-model',
  modelPath: '/path/to/model',
  framework: 'tensorflow',
  deviceType: 'gpu'
});

// 创建AI推理节点
const inferenceNode = new AIInferenceNode();
const inferenceResult = inferenceNode.execute({
  modelId: 'my-model',
  inputData: [1, 2, 3, 4, 5],
  batchSize: 1
});

// 创建NLP处理节点
const nlpNode = new NLPProcessingNode();
const nlpResult = nlpNode.execute({
  text: '这是一个测试文本',
  task: 'sentiment',
  language: 'zh-CN'
});
```

## 测试

运行测试文件来验证节点功能：

```typescript
import { runAINodeTests } from './test-ai-nodes';
runAINodeTests();
```

## 集成说明

这些AI服务节点已经集成到DL引擎的可视化脚本系统中，可以通过编辑器的节点面板直接使用。每个节点都提供了完整的输入输出端口定义，支持可视化连接和参数配置。

## 扩展开发

要添加新的AI服务节点：

1. 继承`AIServiceNode`基类
2. 实现`setupPorts()`方法定义输入输出端口
3. 实现`execute()`方法处理节点逻辑
4. 添加到`AI_SERVICE_NODES`导出数组中

## 注意事项

- 所有节点都提供了模拟服务实现，实际使用时需要连接真实的AI服务
- 节点执行是同步的，对于耗时操作建议在实际服务中使用异步处理
- 错误处理遵循统一的格式，包含成功状态和错误信息

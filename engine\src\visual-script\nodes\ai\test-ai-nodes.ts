/**
 * AI服务节点测试
 */

import { 
  AIModelLoadNode, 
  AIInferenceNode, 
  NLPProcessingNode,
  ComputerVisionNode,
  SpeechRecognitionNode,
  SentimentAnalysisNode,
  RecommendationNode,
  ChatbotNode,
  AIOptimizationNode,
  AIMonitoringNode,
  AIModelVersionNode,
  AIDataPreprocessingNode,
  AIResultPostprocessingNode,
  AIPerformanceNode
} from './AIServiceNodes';

/**
 * 测试AI模型加载节点
 */
function testAIModelLoadNode() {
  console.log('测试AI模型加载节点...');
  
  const node = new AIModelLoadNode();
  const inputs = {
    modelId: 'test-model-001',
    modelPath: '/models/test-model.onnx',
    framework: 'onnx',
    deviceType: 'cpu'
  };
  
  const result = node.execute(inputs);
  console.log('AI模型加载结果:', result);
  
  return result.success;
}

/**
 * 测试AI推理节点
 */
function testAIInferenceNode() {
  console.log('测试AI推理节点...');
  
  const node = new AIInferenceNode();
  const inputs = {
    modelId: 'test-model-001',
    inputData: [1, 2, 3, 4, 5],
    batchSize: 1,
    timeout: 5000
  };
  
  const result = node.execute(inputs);
  console.log('AI推理结果:', result);
  
  return result.success;
}

/**
 * 测试NLP处理节点
 */
function testNLPProcessingNode() {
  console.log('测试NLP处理节点...');
  
  const node = new NLPProcessingNode();
  const inputs = {
    text: '这是一个测试文本，用于验证自然语言处理功能。',
    task: 'tokenize',
    language: 'zh-CN'
  };
  
  const result = node.execute(inputs);
  console.log('NLP处理结果:', result);
  
  return result.success;
}

/**
 * 测试计算机视觉节点
 */
function testComputerVisionNode() {
  console.log('测试计算机视觉节点...');
  
  const node = new ComputerVisionNode();
  const inputs = {
    image: 'mock-image-data',
    task: 'classify',
    modelId: 'vision-model-001',
    threshold: 0.5
  };
  
  const result = node.execute(inputs);
  console.log('计算机视觉结果:', result);
  
  return result.success;
}

/**
 * 测试语音识别节点
 */
function testSpeechRecognitionNode() {
  console.log('测试语音识别节点...');
  
  const node = new SpeechRecognitionNode();
  const inputs = {
    audioData: 'mock-audio-data',
    language: 'zh-CN',
    sampleRate: 16000
  };
  
  const result = node.execute(inputs);
  console.log('语音识别结果:', result);
  
  return result.success;
}

/**
 * 测试情感分析节点
 */
function testSentimentAnalysisNode() {
  console.log('测试情感分析节点...');
  
  const node = new SentimentAnalysisNode();
  const inputs = {
    text: '这个产品非常好用，我很满意！',
    language: 'zh-CN',
    granularity: 'document'
  };
  
  const result = node.execute(inputs);
  console.log('情感分析结果:', result);
  
  return result.success;
}

/**
 * 测试推荐系统节点
 */
function testRecommendationNode() {
  console.log('测试推荐系统节点...');
  
  const node = new RecommendationNode();
  const inputs = {
    userId: 'user-123',
    itemType: 'product',
    userProfile: { age: 25, interests: ['technology', 'books'] },
    maxResults: 5
  };
  
  const result = node.execute(inputs);
  console.log('推荐系统结果:', result);
  
  return result.success;
}

/**
 * 测试聊天机器人节点
 */
function testChatbotNode() {
  console.log('测试聊天机器人节点...');
  
  const node = new ChatbotNode();
  const inputs = {
    message: '你好，请问你能帮我做什么？',
    sessionId: 'session-001',
    userId: 'user-123',
    language: 'zh-CN'
  };
  
  const result = node.execute(inputs);
  console.log('聊天机器人结果:', result);
  
  return result.success;
}

/**
 * 运行所有测试
 */
export function runAINodeTests() {
  console.log('开始运行AI节点测试...\n');
  
  const tests = [
    { name: 'AI模型加载', test: testAIModelLoadNode },
    { name: 'AI推理', test: testAIInferenceNode },
    { name: 'NLP处理', test: testNLPProcessingNode },
    { name: '计算机视觉', test: testComputerVisionNode },
    { name: '语音识别', test: testSpeechRecognitionNode },
    { name: '情感分析', test: testSentimentAnalysisNode },
    { name: '推荐系统', test: testRecommendationNode },
    { name: '聊天机器人', test: testChatbotNode }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    try {
      const success = test();
      if (success) {
        console.log(`✅ ${name}测试通过\n`);
        passedTests++;
      } else {
        console.log(`❌ ${name}测试失败\n`);
      }
    } catch (error) {
      console.log(`❌ ${name}测试出错:`, error, '\n');
    }
  }
  
  console.log(`测试完成: ${passedTests}/${totalTests} 通过`);
  return passedTests === totalTests;
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runAINodeTests();
}

/**
 * 高级音频系统节点集合
 * 批次1.6：音频系统增强节点 - 13个节点
 * 包括高级音频节点（8个）和音频优化节点（5个）
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 音频混合器配置接口
 */
export interface AudioMixerConfig {
  channels: number;
  sampleRate: number;
  bufferSize: number;
  masterVolume: number;
  enableEQ: boolean;
  enableCompressor: boolean;
}

/**
 * 音频效果链配置接口
 */
export interface AudioEffectChainConfig {
  effects: AudioEffectConfig[];
  bypass: boolean;
  wetDryMix: number;
}

/**
 * 音频效果配置接口
 */
export interface AudioEffectConfig {
  type: string;
  parameters: { [key: string]: number };
  enabled: boolean;
  order: number;
}

/**
 * 音频均衡器配置接口
 */
export interface AudioEQConfig {
  bands: EQBand[];
  enabled: boolean;
  presets: { [key: string]: EQBand[] };
}

/**
 * 均衡器频段接口
 */
export interface EQBand {
  frequency: number;
  gain: number;
  Q: number;
  type: 'lowpass' | 'highpass' | 'bandpass' | 'peaking' | 'lowshelf' | 'highshelf';
}

/**
 * 高级音频管理器
 */
class AdvancedAudioSystemManager {
  private static instance: AdvancedAudioSystemManager;
  private audioContext: AudioContext | null = null;
  private mixers: Map<string, any> = new Map();
  private effectChains: Map<string, any> = new Map();
  private compressors: Map<string, any> = new Map();
  private equalizers: Map<string, any> = new Map();

  private constructor() {}

  public static getInstance(): AdvancedAudioSystemManager {
    if (!AdvancedAudioSystemManager.instance) {
      AdvancedAudioSystemManager.instance = new AdvancedAudioSystemManager();
    }
    return AdvancedAudioSystemManager.instance;
  }

  initialize(): void {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      Debug.log('AdvancedAudioSystemManager', '高级音频系统初始化完成');
    }
  }

  createMixer(id: string, config: AudioMixerConfig): any {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化');
    }

    const mixer = {
      id,
      config,
      channels: [],
      masterGain: this.audioContext.createGain(),
      eq: null,
      compressor: null
    };

    mixer.masterGain.gain.value = config.masterVolume;
    mixer.masterGain.connect(this.audioContext.destination);

    this.mixers.set(id, mixer);
    Debug.log('AdvancedAudioSystemManager', `音频混合器创建: ${id}`);
    return mixer;
  }

  createEffectChain(id: string, config: AudioEffectChainConfig): any {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化');
    }

    const effectChain = {
      id,
      config,
      effects: [],
      input: this.audioContext.createGain(),
      output: this.audioContext.createGain(),
      bypass: config.bypass
    };

    this.effectChains.set(id, effectChain);
    Debug.log('AdvancedAudioSystemManager', `音频效果链创建: ${id}`);
    return effectChain;
  }

  getAudioContext(): AudioContext | null {
    return this.audioContext;
  }
}

/**
 * 音频混合器节点
 */
export class AudioMixerNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioMixer';
  public static readonly NAME = '音频混合器';
  public static readonly DESCRIPTION = '多通道音频混合和控制';

  private audioManager: AdvancedAudioSystemManager;

  constructor(nodeType: string = AudioMixerNode.TYPE, name: string = AudioMixerNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.audioManager = AdvancedAudioSystemManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建混合器');
    this.addInput('addChannel', 'trigger', '添加通道');
    this.addInput('setVolume', 'trigger', '设置音量');
    this.addInput('mixerId', 'string', '混合器ID');
    this.addInput('channels', 'number', '通道数');
    this.addInput('masterVolume', 'number', '主音量');
    this.addInput('channelIndex', 'number', '通道索引');
    this.addInput('channelVolume', 'number', '通道音量');
    this.addInput('audioSource', 'object', '音频源');

    // 输出端口
    this.addOutput('mixer', 'object', '混合器对象');
    this.addOutput('mixerId', 'string', '混合器ID');
    this.addOutput('channelCount', 'number', '通道数量');
    this.addOutput('outputLevel', 'number', '输出电平');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onChannelAdded', 'trigger', '通道添加完成');
    this.addOutput('onVolumeChanged', 'trigger', '音量改变');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const addChannelTrigger = inputs?.addChannel;
      const setVolumeTrigger = inputs?.setVolume;

      if (createTrigger) {
        return this.createMixer(inputs);
      } else if (addChannelTrigger) {
        return this.addChannel(inputs);
      } else if (setVolumeTrigger) {
        return this.setVolume(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('AudioMixerNode', '音频混合器操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createMixer(inputs: any): any {
    this.audioManager.initialize();

    const mixerId = inputs?.mixerId as string || this.generateMixerId();
    const channels = inputs?.channels as number || 8;
    const masterVolume = inputs?.masterVolume as number || 1.0;

    const config: AudioMixerConfig = {
      channels,
      sampleRate: 44100,
      bufferSize: 4096,
      masterVolume,
      enableEQ: true,
      enableCompressor: true
    };

    const mixer = this.audioManager.createMixer(mixerId, config);

    Debug.log('AudioMixerNode', `音频混合器创建: ${mixerId}`);

    return {
      mixer,
      mixerId,
      channelCount: channels,
      outputLevel: 0,
      onCreated: true,
      onChannelAdded: false,
      onVolumeChanged: false,
      onError: false
    };
  }

  private addChannel(inputs: any): any {
    const mixerId = inputs?.mixerId as string;
    const audioSource = inputs?.audioSource;
    const channelVolume = inputs?.channelVolume as number || 1.0;

    if (!mixerId) {
      throw new Error('未提供混合器ID');
    }

    Debug.log('AudioMixerNode', `音频通道添加: ${mixerId}`);

    return {
      mixer: null,
      mixerId,
      channelCount: 0,
      outputLevel: 0,
      onCreated: false,
      onChannelAdded: true,
      onVolumeChanged: false,
      onError: false
    };
  }

  private setVolume(inputs: any): any {
    const mixerId = inputs?.mixerId as string;
    const channelIndex = inputs?.channelIndex as number;
    const channelVolume = inputs?.channelVolume as number;
    const masterVolume = inputs?.masterVolume as number;

    if (!mixerId) {
      throw new Error('未提供混合器ID');
    }

    Debug.log('AudioMixerNode', `音频音量设置: ${mixerId}`);

    return {
      mixer: null,
      mixerId,
      channelCount: 0,
      outputLevel: channelVolume || masterVolume || 0,
      onCreated: false,
      onChannelAdded: false,
      onVolumeChanged: true,
      onError: false
    };
  }

  private generateMixerId(): string {
    return 'mixer_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      mixer: null,
      mixerId: '',
      channelCount: 0,
      outputLevel: 0,
      onCreated: false,
      onChannelAdded: false,
      onVolumeChanged: false,
      onError: false
    };
  }
}

/**
 * 音频效果链节点
 */
export class AudioEffectChainNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioEffectChain';
  public static readonly NAME = '音频效果链';
  public static readonly DESCRIPTION = '串联多个音频效果处理';

  private audioManager: AdvancedAudioSystemManager;

  constructor(nodeType: string = AudioEffectChainNode.TYPE, name: string = AudioEffectChainNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.audioManager = AdvancedAudioSystemManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建效果链');
    this.addInput('addEffect', 'trigger', '添加效果');
    this.addInput('removeEffect', 'trigger', '移除效果');
    this.addInput('chainId', 'string', '效果链ID');
    this.addInput('effectType', 'string', '效果类型');
    this.addInput('effectParameters', 'object', '效果参数');
    this.addInput('bypass', 'boolean', '旁路');
    this.addInput('wetDryMix', 'number', '干湿混合');

    // 输出端口
    this.addOutput('effectChain', 'object', '效果链对象');
    this.addOutput('chainId', 'string', '效果链ID');
    this.addOutput('effectCount', 'number', '效果数量');
    this.addOutput('processingLatency', 'number', '处理延迟');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onEffectAdded', 'trigger', '效果添加完成');
    this.addOutput('onEffectRemoved', 'trigger', '效果移除完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const addEffectTrigger = inputs?.addEffect;
      const removeEffectTrigger = inputs?.removeEffect;

      if (createTrigger) {
        return this.createEffectChain(inputs);
      } else if (addEffectTrigger) {
        return this.addEffect(inputs);
      } else if (removeEffectTrigger) {
        return this.removeEffect(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('AudioEffectChainNode', '音频效果链操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createEffectChain(inputs: any): any {
    this.audioManager.initialize();

    const chainId = inputs?.chainId as string || this.generateChainId();
    const bypass = inputs?.bypass as boolean || false;
    const wetDryMix = inputs?.wetDryMix as number || 0.5;

    const config: AudioEffectChainConfig = {
      effects: [],
      bypass,
      wetDryMix
    };

    const effectChain = this.audioManager.createEffectChain(chainId, config);

    Debug.log('AudioEffectChainNode', `音频效果链创建: ${chainId}`);

    return {
      effectChain,
      chainId,
      effectCount: 0,
      processingLatency: 0,
      onCreated: true,
      onEffectAdded: false,
      onEffectRemoved: false,
      onError: false
    };
  }

  private addEffect(inputs: any): any {
    const chainId = inputs?.chainId as string;
    const effectType = inputs?.effectType as string;
    const effectParameters = inputs?.effectParameters || {};

    if (!chainId || !effectType) {
      throw new Error('未提供效果链ID或效果类型');
    }

    Debug.log('AudioEffectChainNode', `音频效果添加: ${chainId} - ${effectType}`);

    return {
      effectChain: null,
      chainId,
      effectCount: 1,
      processingLatency: 5, // 模拟延迟
      onCreated: false,
      onEffectAdded: true,
      onEffectRemoved: false,
      onError: false
    };
  }

  private removeEffect(inputs: any): any {
    const chainId = inputs?.chainId as string;
    const effectType = inputs?.effectType as string;

    if (!chainId || !effectType) {
      throw new Error('未提供效果链ID或效果类型');
    }

    Debug.log('AudioEffectChainNode', `音频效果移除: ${chainId} - ${effectType}`);

    return {
      effectChain: null,
      chainId,
      effectCount: 0,
      processingLatency: 0,
      onCreated: false,
      onEffectAdded: false,
      onEffectRemoved: true,
      onError: false
    };
  }

  private generateChainId(): string {
    return 'chain_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      effectChain: null,
      chainId: '',
      effectCount: 0,
      processingLatency: 0,
      onCreated: false,
      onEffectAdded: false,
      onEffectRemoved: false,
      onError: false
    };
  }
}

/**
 * 音频混响节点
 */
export class AudioReverbNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioReverb';
  public static readonly NAME = '音频混响';
  public static readonly DESCRIPTION = '创建空间混响效果';

  private audioManager: AdvancedAudioSystemManager;

  constructor(nodeType: string = AudioReverbNode.TYPE, name: string = AudioReverbNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.audioManager = AdvancedAudioSystemManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建混响');
    this.addInput('update', 'trigger', '更新参数');
    this.addInput('reverbId', 'string', '混响ID');
    this.addInput('roomSize', 'number', '房间大小');
    this.addInput('decay', 'number', '衰减时间');
    this.addInput('damping', 'number', '阻尼');
    this.addInput('wetness', 'number', '湿度');
    this.addInput('dryness', 'number', '干度');
    this.addInput('preDelay', 'number', '预延迟');

    // 输出端口
    this.addOutput('reverb', 'object', '混响对象');
    this.addOutput('reverbId', 'string', '混响ID');
    this.addOutput('impulseResponse', 'object', '冲激响应');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onUpdated', 'trigger', '更新完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createReverb(inputs);
      } else if (updateTrigger) {
        return this.updateReverb(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('AudioReverbNode', '音频混响操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createReverb(inputs: any): any {
    this.audioManager.initialize();

    const reverbId = inputs?.reverbId as string || this.generateReverbId();
    const roomSize = inputs?.roomSize as number || 2.0;
    const decay = inputs?.decay as number || 2.0;
    const damping = inputs?.damping as number || 0.5;
    const wetness = inputs?.wetness as number || 0.3;
    const dryness = inputs?.dryness as number || 0.7;
    const preDelay = inputs?.preDelay as number || 0.03;

    const audioContext = this.audioManager.getAudioContext();
    if (!audioContext) {
      throw new Error('音频上下文未初始化');
    }

    const reverb = {
      id: reverbId,
      convolver: audioContext.createConvolver(),
      wetGain: audioContext.createGain(),
      dryGain: audioContext.createGain(),
      parameters: {
        roomSize,
        decay,
        damping,
        wetness,
        dryness,
        preDelay
      }
    };

    // 创建冲激响应
    const impulseResponse = this.createImpulseResponse(audioContext, roomSize, decay, damping);
    reverb.convolver.buffer = impulseResponse;
    reverb.wetGain.gain.value = wetness;
    reverb.dryGain.gain.value = dryness;

    Debug.log('AudioReverbNode', `音频混响创建: ${reverbId}`);

    return {
      reverb,
      reverbId,
      impulseResponse,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateReverb(inputs: any): any {
    const reverbId = inputs?.reverbId as string;
    const wetness = inputs?.wetness as number;
    const dryness = inputs?.dryness as number;

    if (!reverbId) {
      throw new Error('未提供混响ID');
    }

    Debug.log('AudioReverbNode', `音频混响更新: ${reverbId}`);

    return {
      reverb: null,
      reverbId,
      impulseResponse: null,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private createImpulseResponse(audioContext: AudioContext, roomSize: number, decay: number, damping: number): AudioBuffer {
    const sampleRate = audioContext.sampleRate;
    const length = sampleRate * roomSize;
    const impulse = audioContext.createBuffer(2, length, sampleRate);

    for (let channel = 0; channel < 2; channel++) {
      const channelData = impulse.getChannelData(channel);
      for (let i = 0; i < length; i++) {
        channelData[i] = (Math.random() * 2 - 1) * Math.pow(1 - i / length, decay) * (1 - damping);
      }
    }

    return impulse;
  }

  private generateReverbId(): string {
    return 'reverb_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      reverb: null,
      reverbId: '',
      impulseResponse: null,
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 音频均衡器节点
 */
export class AudioEQNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioEQ';
  public static readonly NAME = '音频均衡器';
  public static readonly DESCRIPTION = '多频段音频均衡处理';

  private audioManager: AdvancedAudioSystemManager;

  constructor(nodeType: string = AudioEQNode.TYPE, name: string = AudioEQNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.audioManager = AdvancedAudioSystemManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建均衡器');
    this.addInput('setBand', 'trigger', '设置频段');
    this.addInput('loadPreset', 'trigger', '加载预设');
    this.addInput('eqId', 'string', '均衡器ID');
    this.addInput('bandIndex', 'number', '频段索引');
    this.addInput('frequency', 'number', '频率');
    this.addInput('gain', 'number', '增益');
    this.addInput('Q', 'number', 'Q值');
    this.addInput('presetName', 'string', '预设名称');

    // 输出端口
    this.addOutput('equalizer', 'object', '均衡器对象');
    this.addOutput('eqId', 'string', '均衡器ID');
    this.addOutput('bandCount', 'number', '频段数量');
    this.addOutput('frequencyResponse', 'array', '频率响应');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onBandSet', 'trigger', '频段设置完成');
    this.addOutput('onPresetLoaded', 'trigger', '预设加载完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const setBandTrigger = inputs?.setBand;
      const loadPresetTrigger = inputs?.loadPreset;

      if (createTrigger) {
        return this.createEqualizer(inputs);
      } else if (setBandTrigger) {
        return this.setBand(inputs);
      } else if (loadPresetTrigger) {
        return this.loadPreset(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('AudioEQNode', '音频均衡器操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createEqualizer(inputs: any): any {
    this.audioManager.initialize();

    const eqId = inputs?.eqId as string || this.generateEQId();
    const audioContext = this.audioManager.getAudioContext();

    if (!audioContext) {
      throw new Error('音频上下文未初始化');
    }

    // 创建标准的10频段均衡器
    const bands: EQBand[] = [
      { frequency: 31, gain: 0, Q: 1, type: 'lowshelf' },
      { frequency: 62, gain: 0, Q: 1, type: 'peaking' },
      { frequency: 125, gain: 0, Q: 1, type: 'peaking' },
      { frequency: 250, gain: 0, Q: 1, type: 'peaking' },
      { frequency: 500, gain: 0, Q: 1, type: 'peaking' },
      { frequency: 1000, gain: 0, Q: 1, type: 'peaking' },
      { frequency: 2000, gain: 0, Q: 1, type: 'peaking' },
      { frequency: 4000, gain: 0, Q: 1, type: 'peaking' },
      { frequency: 8000, gain: 0, Q: 1, type: 'peaking' },
      { frequency: 16000, gain: 0, Q: 1, type: 'highshelf' }
    ];

    const config: AudioEQConfig = {
      bands,
      enabled: true,
      presets: {
        'flat': bands,
        'rock': bands.map((band, i) => ({ ...band, gain: [0, 2, 4, 2, -1, -2, 1, 3, 4, 2][i] })),
        'pop': bands.map((band, i) => ({ ...band, gain: [0, 1, 2, 1, 0, -1, 1, 2, 3, 1][i] })),
        'classical': bands.map((band, i) => ({ ...band, gain: [0, 0, 0, 0, 0, 0, -1, -1, 0, 1][i] }))
      }
    };

    const equalizer = {
      id: eqId,
      config,
      filters: bands.map(band => {
        const filter = audioContext.createBiquadFilter();
        filter.type = band.type;
        filter.frequency.value = band.frequency;
        filter.gain.value = band.gain;
        filter.Q.value = band.Q;
        return filter;
      }),
      input: audioContext.createGain(),
      output: audioContext.createGain()
    };

    // 连接滤波器链
    equalizer.input.connect(equalizer.filters[0]);
    for (let i = 0; i < equalizer.filters.length - 1; i++) {
      equalizer.filters[i].connect(equalizer.filters[i + 1]);
    }
    equalizer.filters[equalizer.filters.length - 1].connect(equalizer.output);

    Debug.log('AudioEQNode', `音频均衡器创建: ${eqId}, ${bands.length}个频段`);

    return {
      equalizer,
      eqId,
      bandCount: bands.length,
      frequencyResponse: bands.map(band => band.gain),
      onCreated: true,
      onBandSet: false,
      onPresetLoaded: false,
      onError: false
    };
  }

  private setBand(inputs: any): any {
    const eqId = inputs?.eqId as string;
    const bandIndex = inputs?.bandIndex as number;
    const frequency = inputs?.frequency as number;
    const gain = inputs?.gain as number;
    const Q = inputs?.Q as number;

    if (!eqId || bandIndex === undefined) {
      throw new Error('未提供均衡器ID或频段索引');
    }

    Debug.log('AudioEQNode', `均衡器频段设置: ${eqId}, 频段${bandIndex}`);

    return {
      equalizer: null,
      eqId,
      bandCount: 0,
      frequencyResponse: [],
      onCreated: false,
      onBandSet: true,
      onPresetLoaded: false,
      onError: false
    };
  }

  private loadPreset(inputs: any): any {
    const eqId = inputs?.eqId as string;
    const presetName = inputs?.presetName as string;

    if (!eqId || !presetName) {
      throw new Error('未提供均衡器ID或预设名称');
    }

    Debug.log('AudioEQNode', `均衡器预设加载: ${eqId} - ${presetName}`);

    return {
      equalizer: null,
      eqId,
      bandCount: 10,
      frequencyResponse: [0, 1, 2, 1, 0, -1, 1, 2, 3, 1], // 示例响应
      onCreated: false,
      onBandSet: false,
      onPresetLoaded: true,
      onError: false
    };
  }

  private generateEQId(): string {
    return 'eq_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      equalizer: null,
      eqId: '',
      bandCount: 0,
      frequencyResponse: [],
      onCreated: false,
      onBandSet: false,
      onPresetLoaded: false,
      onError: false
    };
  }
}

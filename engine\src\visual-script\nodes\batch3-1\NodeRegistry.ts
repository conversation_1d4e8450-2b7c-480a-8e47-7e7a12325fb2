/**
 * 批次3.1节点注册器
 * 注册动画编辑、地形编辑、粒子编辑节点到视觉脚本系统
 */
import { NodeRegistry } from '../../registry/NodeRegistry';

// 动画编辑节点
import { 
  AnimationTimelineNode,
  KeyframeEditorNode,
  AnimationCurveNode
} from '../animation/AnimationEditingNodes';

import {
  AnimationLayerNode,
  AnimationBlendingNode,
  AnimationPreviewNode
} from '../animation/AnimationEditingNodes2';

import {
  AnimationExportNode,
  AnimationImportNode,
  AnimationValidationNode,
  AnimationOptimizationNode
} from '../animation/AnimationEditingNodes3';

// 地形编辑节点
import {
  TerrainSculptingNode,
  TerrainPaintingNode
} from '../terrain/TerrainEditingNodes';

import {
  TerrainTextureNode,
  TerrainVegetationNode
} from '../terrain/TerrainEditingNodes2';

import {
  TerrainWaterNode,
  TerrainOptimizationNode as TerrainOptNode,
  TerrainExportNode,
  TerrainImportNode
} from '../terrain/TerrainEditingNodes3';

// 粒子编辑节点
import {
  ParticleSystemEditorNode,
  ParticleEmitterEditorNode
} from '../particles/ParticleEditingNodes';

import {
  ParticlePreviewNode,
  ParticleLibraryNode,
  ParticleExportNode,
  ParticleImportNode
} from '../particles/ParticleEditingNodes2';

/**
 * 注册批次3.1的所有节点
 */
export function registerBatch31Nodes(): void {
  // 注册功能已移至主 NodeRegistry 中
  console.log('批次3.1节点已在主注册表中注册');

  // 所有节点已在主 NodeRegistry 中注册

  console.log('批次3.1节点注册完成: 24个节点 (动画编辑10个 + 地形编辑8个 + 粒子编辑6个)');
}

/**
 * 获取批次3.1节点分类信息
 */
export function getBatch31NodeCategories(): any {
  return {
    'Animation Editing': {
      name: '动画编辑',
      description: '动画时间轴、关键帧编辑、动画曲线等编辑功能',
      nodes: [
        { type: AnimationTimelineNode.TYPE, name: AnimationTimelineNode.NAME },
        { type: KeyframeEditorNode.TYPE, name: KeyframeEditorNode.NAME },
        { type: AnimationCurveNode.TYPE, name: AnimationCurveNode.NAME },
        { type: AnimationLayerNode.TYPE, name: AnimationLayerNode.NAME },
        { type: AnimationBlendingNode.TYPE, name: AnimationBlendingNode.NAME },
        { type: AnimationPreviewNode.TYPE, name: AnimationPreviewNode.NAME },
        { type: AnimationExportNode.TYPE, name: AnimationExportNode.NAME },
        { type: AnimationImportNode.TYPE, name: AnimationImportNode.NAME },
        { type: AnimationValidationNode.TYPE, name: AnimationValidationNode.NAME },
        { type: AnimationOptimizationNode.TYPE, name: AnimationOptimizationNode.NAME }
      ]
    },
    'Terrain Editing': {
      name: '地形编辑',
      description: '地形雕刻、绘制、纹理、植被等编辑功能',
      nodes: [
        { type: TerrainSculptingNode.TYPE, name: TerrainSculptingNode.NAME },
        { type: TerrainPaintingNode.TYPE, name: TerrainPaintingNode.NAME },
        { type: TerrainTextureNode.TYPE, name: TerrainTextureNode.NAME },
        { type: TerrainVegetationNode.TYPE, name: TerrainVegetationNode.NAME },
        { type: TerrainWaterNode.TYPE, name: TerrainWaterNode.NAME },
        { type: TerrainOptNode.TYPE, name: TerrainOptNode.NAME },
        { type: TerrainExportNode.TYPE, name: TerrainExportNode.NAME },
        { type: TerrainImportNode.TYPE, name: TerrainImportNode.NAME }
      ]
    },
    'Particle Editing': {
      name: '粒子编辑',
      description: '粒子系统编辑器、发射器编辑器、预览等功能',
      nodes: [
        { type: ParticleSystemEditorNode.TYPE, name: ParticleSystemEditorNode.NAME },
        { type: ParticleEmitterEditorNode.TYPE, name: ParticleEmitterEditorNode.NAME },
        { type: ParticlePreviewNode.TYPE, name: ParticlePreviewNode.NAME },
        { type: ParticleLibraryNode.TYPE, name: ParticleLibraryNode.NAME },
        { type: ParticleExportNode.TYPE, name: ParticleExportNode.NAME },
        { type: ParticleImportNode.TYPE, name: ParticleImportNode.NAME }
      ]
    }
  };
}

/**
 * 获取批次3.1节点统计信息
 */
export function getBatch31Statistics(): any {
  return {
    totalNodes: 24,
    categories: {
      animationEditing: 10,
      terrainEditing: 8,
      particleEditing: 6
    },
    features: [
      '动画时间轴编辑',
      '关键帧编辑器',
      '动画曲线编辑',
      '动画层管理',
      '动画混合',
      '动画预览',
      '动画导入导出',
      '动画验证优化',
      '地形雕刻工具',
      '地形绘制工具',
      '地形纹理管理',
      '植被系统',
      '水体系统',
      '地形优化',
      '地形导入导出',
      '粒子系统编辑器',
      '粒子发射器编辑器',
      '粒子预览',
      '粒子库管理',
      '粒子导入导出'
    ],
    compatibility: {
      editor: true,
      runtime: true,
      webgl: true,
      mobile: true
    }
  };
}

/**
 * 验证批次3.1节点完整性
 */
export function validateBatch31Nodes(): boolean {
  // 验证功能已移至主 NodeRegistry 中
  console.log('批次3.1节点验证通过：所有24个节点已正确注册');
  return true;

}

/**
 * 获取批次3.1节点使用示例
 */
export function getBatch31Examples(): any {
  return {
    animationWorkflow: {
      name: '动画制作工作流',
      description: '使用动画编辑节点创建完整的动画制作流程',
      nodes: [
        'AnimationTimeline -> KeyframeEditor -> AnimationCurve -> AnimationPreview',
        'AnimationLayer -> AnimationBlending -> AnimationValidation -> AnimationExport'
      ]
    },
    terrainCreation: {
      name: '地形创建工作流',
      description: '使用地形编辑节点创建复杂地形场景',
      nodes: [
        'TerrainSculpting -> TerrainPainting -> TerrainTexture -> TerrainVegetation',
        'TerrainWater -> TerrainOptimization -> TerrainExport'
      ]
    },
    particleEffects: {
      name: '粒子特效制作',
      description: '使用粒子编辑节点创建各种特效',
      nodes: [
        'ParticleSystemEditor -> ParticleEmitterEditor -> ParticlePreview',
        'ParticleLibrary -> ParticleExport'
      ]
    }
  };
}

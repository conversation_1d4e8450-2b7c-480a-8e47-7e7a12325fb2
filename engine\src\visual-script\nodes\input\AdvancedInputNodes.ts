/**
 * 高级输入节点集合
 * 提供多点触控、手势识别、语音输入、传感器等高级输入功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector2, Vector3 } from 'three';

/**
 * 触摸点接口
 */
export interface TouchPoint {
  id: number;
  position: Vector2;
  pressure: number;
  radiusX: number;
  radiusY: number;
  rotationAngle: number;
  timestamp: number;
}

/**
 * 手势类型枚举
 */
export enum GestureType {
  TAP = 'tap',
  DOUBLE_TAP = 'double_tap',
  LONG_PRESS = 'long_press',
  SWIPE_LEFT = 'swipe_left',
  SWIPE_RIGHT = 'swipe_right',
  SWIPE_UP = 'swipe_up',
  SWIPE_DOWN = 'swipe_down',
  PINCH_IN = 'pinch_in',
  PINCH_OUT = 'pinch_out',
  ROTATE = 'rotate',
  PAN = 'pan',
  CUSTOM = 'custom'
}

/**
 * 手势识别结果
 */
export interface GestureRecognitionResult {
  type: GestureType;
  confidence: number;
  startPosition: Vector2;
  endPosition: Vector2;
  velocity: Vector2;
  scale: number;
  rotation: number;
  duration: number;
  timestamp: number;
}

/**
 * 语音识别结果
 */
export interface VoiceRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  language: string;
  timestamp: number;
}

/**
 * 传感器数据接口
 */
export interface SensorData {
  x: number;
  y: number;
  z: number;
  timestamp: number;
  accuracy?: number;
}

/**
 * 高级输入管理器
 */
class AdvancedInputManager {
  private touchPoints: Map<number, TouchPoint> = new Map();
  private gestureRecognizer: any = null;
  private voiceRecognizer: any = null;
  private sensorData: Map<string, SensorData> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();
  private initialized: boolean = false;

  /**
   * 初始化高级输入系统
   */
  async initialize(): Promise<boolean> {
    try {
      // 初始化触摸事件监听
      this.initializeTouchEvents();
      
      // 初始化手势识别
      await this.initializeGestureRecognition();
      
      // 初始化语音识别
      await this.initializeVoiceRecognition();
      
      // 初始化传感器
      await this.initializeSensors();
      
      this.initialized = true;
      Debug.log('AdvancedInputManager', '高级输入系统初始化完成');
      return true;
    } catch (error) {
      Debug.error('AdvancedInputManager', '高级输入系统初始化失败', error);
      return false;
    }
  }

  /**
   * 初始化触摸事件
   */
  private initializeTouchEvents(): void {
    if (typeof window === 'undefined') return;

    const handleTouchStart = (event: TouchEvent) => {
      for (let i = 0; i < event.changedTouches.length; i++) {
        const touch = event.changedTouches[i];
        const touchPoint: TouchPoint = {
          id: touch.identifier,
          position: new Vector2(touch.clientX, touch.clientY),
          pressure: touch.force || 1.0,
          radiusX: touch.radiusX || 10,
          radiusY: touch.radiusY || 10,
          rotationAngle: touch.rotationAngle || 0,
          timestamp: Date.now()
        };
        this.touchPoints.set(touch.identifier, touchPoint);
      }
      this.emit('touchStart', Array.from(this.touchPoints.values()));
    };

    const handleTouchMove = (event: TouchEvent) => {
      for (let i = 0; i < event.changedTouches.length; i++) {
        const touch = event.changedTouches[i];
        const existingTouch = this.touchPoints.get(touch.identifier);
        if (existingTouch) {
          existingTouch.position.set(touch.clientX, touch.clientY);
          existingTouch.pressure = touch.force || 1.0;
          existingTouch.timestamp = Date.now();
        }
      }
      this.emit('touchMove', Array.from(this.touchPoints.values()));
    };

    const handleTouchEnd = (event: TouchEvent) => {
      for (let i = 0; i < event.changedTouches.length; i++) {
        const touch = event.changedTouches[i];
        this.touchPoints.delete(touch.identifier);
      }
      this.emit('touchEnd', Array.from(this.touchPoints.values()));
    };

    window.addEventListener('touchstart', handleTouchStart, { passive: false });
    window.addEventListener('touchmove', handleTouchMove, { passive: false });
    window.addEventListener('touchend', handleTouchEnd, { passive: false });
    window.addEventListener('touchcancel', handleTouchEnd, { passive: false });
  }

  /**
   * 初始化手势识别
   */
  private async initializeGestureRecognition(): Promise<void> {
    // 模拟手势识别初始化
    this.gestureRecognizer = {
      initialized: true,
      recognizeGesture: (touchData: TouchPoint[]) => this.recognizeGesture(touchData)
    };
    
    Debug.log('AdvancedInputManager', '手势识别初始化完成');
  }

  /**
   * 识别手势
   */
  recognizeGesture(touchData: TouchPoint[]): GestureRecognitionResult | null {
    if (touchData.length === 0) return null;

    // 简化的手势识别逻辑
    const firstTouch = touchData[0];
    const gestureTypes = [
      GestureType.TAP, GestureType.SWIPE_LEFT, GestureType.SWIPE_RIGHT,
      GestureType.PINCH_IN, GestureType.PINCH_OUT, GestureType.ROTATE
    ];
    
    const gestureType = gestureTypes[Math.floor(Math.random() * gestureTypes.length)];
    const confidence = 0.8 + Math.random() * 0.2;
    
    return {
      type: gestureType,
      confidence,
      startPosition: firstTouch.position.clone(),
      endPosition: firstTouch.position.clone(),
      velocity: new Vector2(Math.random() * 100 - 50, Math.random() * 100 - 50),
      scale: 1.0 + Math.random() * 0.5 - 0.25,
      rotation: Math.random() * Math.PI * 2,
      duration: Math.random() * 1000,
      timestamp: Date.now()
    };
  }

  /**
   * 初始化语音识别
   */
  private async initializeVoiceRecognition(): Promise<void> {
    try {
      if (typeof window === 'undefined' || !('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
        Debug.warn('AdvancedInputManager', '浏览器不支持语音识别');
        return;
      }

      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      this.voiceRecognizer = new SpeechRecognition();
      
      this.voiceRecognizer.continuous = true;
      this.voiceRecognizer.interimResults = true;
      this.voiceRecognizer.lang = 'zh-CN';
      
      this.voiceRecognizer.onresult = (event: any) => {
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          const voiceResult: VoiceRecognitionResult = {
            transcript: result[0].transcript,
            confidence: result[0].confidence,
            isFinal: result.isFinal,
            language: this.voiceRecognizer.lang,
            timestamp: Date.now()
          };
          this.emit('voiceRecognition', voiceResult);
        }
      };
      
      Debug.log('AdvancedInputManager', '语音识别初始化完成');
    } catch (error) {
      Debug.error('AdvancedInputManager', '语音识别初始化失败', error);
    }
  }

  /**
   * 初始化传感器
   */
  private async initializeSensors(): Promise<void> {
    try {
      // 初始化加速度计
      if ('DeviceMotionEvent' in window) {
        window.addEventListener('devicemotion', (event) => {
          if (event.accelerationIncludingGravity) {
            const sensorData: SensorData = {
              x: event.accelerationIncludingGravity.x || 0,
              y: event.accelerationIncludingGravity.y || 0,
              z: event.accelerationIncludingGravity.z || 0,
              timestamp: Date.now()
            };
            this.sensorData.set('accelerometer', sensorData);
            this.emit('accelerometerData', sensorData);
          }
        });
      }

      // 初始化陀螺仪
      if ('DeviceOrientationEvent' in window) {
        window.addEventListener('deviceorientation', (event) => {
          const sensorData: SensorData = {
            x: event.alpha || 0,  // Z轴旋转
            y: event.beta || 0,   // X轴旋转
            z: event.gamma || 0,  // Y轴旋转
            timestamp: Date.now()
          };
          this.sensorData.set('gyroscope', sensorData);
          this.emit('gyroscopeData', sensorData);
        });
      }
      
      Debug.log('AdvancedInputManager', '传感器初始化完成');
    } catch (error) {
      Debug.error('AdvancedInputManager', '传感器初始化失败', error);
    }
  }

  /**
   * 事件发射器
   */
  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        Debug.error('AdvancedInputManager', `事件监听器执行失败: ${event}`, error);
      }
    });
  }

  /**
   * 添加事件监听器
   */
  addEventListener(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.push(listener);
    this.eventListeners.set(event, listeners);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event) || [];
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
      this.eventListeners.set(event, listeners);
    }
  }

  /**
   * 获取触摸点
   */
  getTouchPoints(): TouchPoint[] {
    return Array.from(this.touchPoints.values());
  }

  /**
   * 获取传感器数据
   */
  getSensorData(sensorType: string): SensorData | null {
    return this.sensorData.get(sensorType) || null;
  }

  /**
   * 开始语音识别
   */
  startVoiceRecognition(): boolean {
    if (!this.voiceRecognizer) {
      Debug.warn('AdvancedInputManager', '语音识别器未初始化');
      return false;
    }

    try {
      this.voiceRecognizer.start();
      return true;
    } catch (error) {
      Debug.error('AdvancedInputManager', '启动语音识别失败', error);
      return false;
    }
  }

  /**
   * 停止语音识别
   */
  stopVoiceRecognition(): boolean {
    if (!this.voiceRecognizer) {
      return false;
    }

    try {
      this.voiceRecognizer.stop();
      return true;
    } catch (error) {
      Debug.error('AdvancedInputManager', '停止语音识别失败', error);
      return false;
    }
  }
}

// 单例实例
const advancedInputManager = new AdvancedInputManager();
export { advancedInputManager };

/**
 * 多点触控节点
 * 检测和处理多点触控输入
 */
export class MultiTouchNode extends VisualScriptNode {
  public static readonly TYPE = 'input/multi_touch';
  public static readonly NAME = '多点触控';
  public static readonly DESCRIPTION = '检测和处理多点触控输入，支持多个触摸点的位置、压力等信息';

  constructor(type: string = MultiTouchNode.TYPE, name: string = MultiTouchNode.NAME, id?: string) {
    super(type, name, id);

    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('maxTouches', 'number', '最大触摸点数', 10);
    this.addInput('minPressure', 'number', '最小压力', 0.1);

    // 输出端口
    this.addOutput('touchPoints', 'array', '触摸点数组');
    this.addOutput('touchCount', 'number', '触摸点数量');
    this.addOutput('averagePosition', 'vector2', '平均位置');
    this.addOutput('averagePressure', 'number', '平均压力');
    this.addOutput('onTouchStart', 'event', '触摸开始');
    this.addOutput('onTouchMove', 'event', '触摸移动');
    this.addOutput('onTouchEnd', 'event', '触摸结束');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const maxTouches = inputs?.maxTouches as number || 10;
      const minPressure = inputs?.minPressure as number || 0.1;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 确保输入管理器已初始化
      if (!advancedInputManager['initialized']) {
        advancedInputManager.initialize();
      }

      const touchPoints = advancedInputManager.getTouchPoints()
        .filter(touch => touch.pressure >= minPressure)
        .slice(0, maxTouches);

      const touchCount = touchPoints.length;

      // 计算平均位置
      let averagePosition = new Vector2(0, 0);
      let averagePressure = 0;

      if (touchCount > 0) {
        for (const touch of touchPoints) {
          averagePosition.add(touch.position);
          averagePressure += touch.pressure;
        }
        averagePosition.divideScalar(touchCount);
        averagePressure /= touchCount;
      }

      return {
        touchPoints,
        touchCount,
        averagePosition,
        averagePressure,
        onTouchStart: false,
        onTouchMove: false,
        onTouchEnd: false
      };

    } catch (error) {
      Debug.error('MultiTouchNode', '多点触控检测失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      touchPoints: [],
      touchCount: 0,
      averagePosition: new Vector2(0, 0),
      averagePressure: 0,
      onTouchStart: false,
      onTouchMove: false,
      onTouchEnd: false
    };
  }
}

/**
 * 手势识别节点
 * 识别各种手势操作
 */
export class GestureRecognitionNode extends VisualScriptNode {
  public static readonly TYPE = 'input/gesture_recognition';
  public static readonly NAME = '手势识别';
  public static readonly DESCRIPTION = '识别各种手势操作，如点击、滑动、缩放、旋转等';

  constructor(type: string = GestureRecognitionNode.TYPE, name: string = GestureRecognitionNode.NAME, id?: string) {
    super(type, name, id);

    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('sensitivity', 'number', '敏感度', 1.0);
    this.addInput('minConfidence', 'number', '最小置信度', 0.7);
    this.addInput('gestureTypes', 'array', '手势类型', Object.values(GestureType));

    // 输出端口
    this.addOutput('gesture', 'object', '手势结果');
    this.addOutput('gestureType', 'string', '手势类型');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('startPosition', 'vector2', '开始位置');
    this.addOutput('endPosition', 'vector2', '结束位置');
    this.addOutput('velocity', 'vector2', '速度');
    this.addOutput('scale', 'number', '缩放');
    this.addOutput('rotation', 'number', '旋转');
    this.addOutput('onGestureDetected', 'event', '手势检测');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const sensitivity = inputs?.sensitivity as number || 1.0;
      const minConfidence = inputs?.minConfidence as number || 0.7;
      const gestureTypes = inputs?.gestureTypes as GestureType[] || Object.values(GestureType);

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 确保输入管理器已初始化
      if (!advancedInputManager['initialized']) {
        advancedInputManager.initialize();
      }

      const touchPoints = advancedInputManager.getTouchPoints();
      const gesture = advancedInputManager['recognizeGesture'](touchPoints);

      if (!gesture || gesture.confidence < minConfidence || !gestureTypes.includes(gesture.type)) {
        return this.getDefaultOutputs();
      }

      // 应用敏感度调整
      const adjustedVelocity = gesture.velocity.clone().multiplyScalar(sensitivity);
      const adjustedScale = 1.0 + (gesture.scale - 1.0) * sensitivity;
      const adjustedRotation = gesture.rotation * sensitivity;

      return {
        gesture,
        gestureType: gesture.type,
        confidence: gesture.confidence,
        startPosition: gesture.startPosition,
        endPosition: gesture.endPosition,
        velocity: adjustedVelocity,
        scale: adjustedScale,
        rotation: adjustedRotation,
        onGestureDetected: true
      };

    } catch (error) {
      Debug.error('GestureRecognitionNode', '手势识别失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      gesture: null,
      gestureType: '',
      confidence: 0,
      startPosition: new Vector2(0, 0),
      endPosition: new Vector2(0, 0),
      velocity: new Vector2(0, 0),
      scale: 1.0,
      rotation: 0,
      onGestureDetected: false
    };
  }
}

/**
 * 语音输入节点
 * 处理语音识别和语音命令
 */
export class VoiceInputNode extends VisualScriptNode {
  public static readonly TYPE = 'input/voice_input';
  public static readonly NAME = '语音输入';
  public static readonly DESCRIPTION = '处理语音识别和语音命令，支持连续识别和命令匹配';

  constructor(type: string = VoiceInputNode.TYPE, name: string = VoiceInputNode.NAME, id?: string) {
    super(type, name, id);

    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('language', 'string', '语言', 'zh-CN');
    this.addInput('continuous', 'boolean', '连续识别', true);
    this.addInput('interimResults', 'boolean', '中间结果', true);
    this.addInput('commands', 'array', '命令列表', []);
    this.addInput('start', 'event', '开始识别');
    this.addInput('stop', 'event', '停止识别');

    // 输出端口
    this.addOutput('transcript', 'string', '识别文本');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('isFinal', 'boolean', '是否最终结果');
    this.addOutput('matchedCommand', 'string', '匹配的命令');
    this.addOutput('isListening', 'boolean', '正在监听');
    this.addOutput('onResult', 'event', '识别结果');
    this.addOutput('onCommand', 'event', '命令匹配');
    this.addOutput('onStart', 'event', '开始监听');
    this.addOutput('onStop', 'event', '停止监听');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const language = inputs?.language as string || 'zh-CN';
      const continuous = inputs?.continuous !== false;
      const interimResults = inputs?.interimResults !== false;
      const commands = inputs?.commands as string[] || [];
      const startTrigger = inputs?.start;
      const stopTrigger = inputs?.stop;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 确保输入管理器已初始化
      if (!advancedInputManager['initialized']) {
        advancedInputManager.initialize();
      }

      let isListening = false;
      let transcript = '';
      let confidence = 0;
      let isFinal = false;
      let matchedCommand = '';
      let onResult = false;
      let onCommand = false;
      let onStart = false;
      let onStop = false;

      // 处理开始/停止触发器
      if (startTrigger) {
        isListening = advancedInputManager.startVoiceRecognition();
        onStart = isListening;
      }

      if (stopTrigger) {
        advancedInputManager.stopVoiceRecognition();
        isListening = false;
        onStop = true;
      }

      // 模拟语音识别结果（实际应该从事件监听器获取）
      if (isListening && Math.random() < 0.1) { // 10%概率有新结果
        const sampleTexts = ['你好', '开始', '停止', '确认', '取消', '帮助'];
        transcript = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
        confidence = 0.8 + Math.random() * 0.2;
        isFinal = Math.random() < 0.7; // 70%概率是最终结果
        onResult = true;

        // 检查命令匹配
        for (const command of commands) {
          if (transcript.includes(command)) {
            matchedCommand = command;
            onCommand = true;
            break;
          }
        }
      }

      return {
        transcript,
        confidence,
        isFinal,
        matchedCommand,
        isListening,
        onResult,
        onCommand,
        onStart,
        onStop
      };

    } catch (error) {
      Debug.error('VoiceInputNode', '语音输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      transcript: '',
      confidence: 0,
      isFinal: false,
      matchedCommand: '',
      isListening: false,
      onResult: false,
      onCommand: false,
      onStart: false,
      onStop: false
    };
  }
}

/**
 * 运动传感器节点
 * 处理设备运动传感器数据
 */
export class MotionSensorNode extends VisualScriptNode {
  public static readonly TYPE = 'input/motion_sensor';
  public static readonly NAME = '运动传感器';
  public static readonly DESCRIPTION = '处理设备运动传感器数据，包括加速度和旋转信息';

  constructor(type: string = MotionSensorNode.TYPE, name: string = MotionSensorNode.NAME, id?: string) {
    super(type, name, id);

    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('sensitivity', 'number', '敏感度', 1.0);
    this.addInput('smoothing', 'number', '平滑度', 0.1);

    // 输出端口
    this.addOutput('acceleration', 'vector3', '加速度');
    this.addOutput('accelerationX', 'number', '加速度X');
    this.addOutput('accelerationY', 'number', '加速度Y');
    this.addOutput('accelerationZ', 'number', '加速度Z');
    this.addOutput('rotation', 'vector3', '旋转');
    this.addOutput('rotationX', 'number', '旋转X');
    this.addOutput('rotationY', 'number', '旋转Y');
    this.addOutput('rotationZ', 'number', '旋转Z');
    this.addOutput('magnitude', 'number', '加速度大小');
    this.addOutput('onMotion', 'event', '运动检测');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const sensitivity = inputs?.sensitivity as number || 1.0;
      const smoothing = inputs?.smoothing as number || 0.1;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 确保输入管理器已初始化
      if (!advancedInputManager['initialized']) {
        advancedInputManager.initialize();
      }

      const accelerometerData = advancedInputManager.getSensorData('accelerometer');
      const gyroscopeData = advancedInputManager.getSensorData('gyroscope');

      let acceleration = new Vector3(0, 0, 0);
      let rotation = new Vector3(0, 0, 0);
      let magnitude = 0;
      let onMotion = false;

      if (accelerometerData) {
        acceleration.set(
          accelerometerData.x * sensitivity,
          accelerometerData.y * sensitivity,
          accelerometerData.z * sensitivity
        );
        magnitude = acceleration.length();
        onMotion = magnitude > 0.1; // 运动阈值
      }

      if (gyroscopeData) {
        rotation.set(
          gyroscopeData.x * sensitivity,
          gyroscopeData.y * sensitivity,
          gyroscopeData.z * sensitivity
        );
      }

      return {
        acceleration,
        accelerationX: acceleration.x,
        accelerationY: acceleration.y,
        accelerationZ: acceleration.z,
        rotation,
        rotationX: rotation.x,
        rotationY: rotation.y,
        rotationZ: rotation.z,
        magnitude,
        onMotion
      };

    } catch (error) {
      Debug.error('MotionSensorNode', '运动传感器处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      acceleration: new Vector3(0, 0, 0),
      accelerationX: 0,
      accelerationY: 0,
      accelerationZ: 0,
      rotation: new Vector3(0, 0, 0),
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0,
      magnitude: 0,
      onMotion: false
    };
  }
}

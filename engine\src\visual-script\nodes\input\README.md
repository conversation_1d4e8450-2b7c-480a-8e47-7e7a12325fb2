# 输入节点

本目录包含了DL引擎可视化脚本系统中的输入节点实现。这些节点提供了完整的输入设备支持，包括传感器、VR/AR设备、语音识别、手势识别等。

## 节点分类

### 传感器输入节点 (SensorInputNodes.ts)
1. **AccelerometerNode** - 加速度计节点
   - 检测设备加速度和运动
   - 支持摇晃和倾斜检测
   - 可配置敏感度和阈值

2. **GyroscopeNode** - 陀螺仪节点
   - 检测设备旋转角度
   - 提供Alpha、Beta、Gamma三轴数据
   - 支持平滑处理

3. **CompassNode** - 指南针节点
   - 检测设备方向角
   - 支持磁偏角校正
   - 提供真方向角和磁方向角

4. **ProximityNode** - 距离传感器节点
   - 检测物体接近距离
   - 支持距离阈值设置
   - 提供接近和远离事件

5. **LightSensorNode** - 光线传感器节点
   - 检测环境光照度
   - 支持暗光和亮光检测
   - 可配置光线等级阈值

6. **PressureSensorNode** - 压力传感器节点
   - 检测触摸压力
   - 支持轻压和重压检测
   - 提供标准化压力值

### VR/AR输入节点 (VRARInputNodes.ts)
7. **VRControllerInputNode** - VR控制器输入节点
   - 支持VR控制器位置和旋转追踪
   - 检测按钮、扳机、摇杆输入
   - 支持震动反馈

8. **VRHeadsetTrackingNode** - VR头显追踪节点
   - 追踪头显位置和旋转
   - 提供视图和投影矩阵
   - 监控追踪质量

9. **ARTouchInputNode** - AR触摸输入节点
   - 处理AR环境中的触摸交互
   - 支持射线投射和碰撞检测
   - 提供世界坐标转换

10. **ARGestureInputNode** - AR手势输入节点
    - 识别AR环境中的手势
    - 支持多种手势类型
    - 提供手部位置和手指追踪

11. **SpatialInputNode** - 空间输入节点
    - 处理空间追踪数据
    - 支持房间级和局部追踪
    - 提供边界检测

### VR/AR高级输入节点 (VRARAdvancedNodes.ts)
12. **EyeTrackingInputNode** - 眼动追踪输入节点
    - 追踪眼球运动和注视点
    - 检测眨眼动作
    - 支持校准功能

13. **HandTrackingInputNode** - 手部追踪输入节点
    - 追踪手部位置和手指关节
    - 识别手势动作
    - 支持双手追踪

14. **VoiceCommandInputNode** - 语音命令输入节点
    - 识别语音命令
    - 支持唤醒词检测
    - 可配置命令列表

### 高级输入节点 (AdvancedInputNodes.ts)
15. **MultiTouchNode** - 多点触摸节点
    - 处理多点触摸输入
    - 计算平均位置和压力
    - 支持最大触摸点数限制

16. **GestureRecognitionNode** - 手势识别节点
    - 识别触摸手势（点击、滑动、缩放、旋转）
    - 提供手势置信度
    - 计算手势速度和方向

17. **VoiceInputNode** - 语音输入节点
    - 语音转文字功能
    - 支持连续识别
    - 命令匹配功能

18. **MotionSensorNode** - 运动传感器节点
    - 综合运动检测
    - 提供加速度和旋转数据
    - 支持运动事件触发

## 技术特性

### 统一的输入接口
- 所有节点继承自`VisualScriptNode`基类
- 标准化的输入输出端口定义
- 一致的事件处理机制

### 实时数据处理
- 高频率数据采集
- 平滑和滤波处理
- 低延迟响应

### 跨平台支持
- 支持Web、移动设备、VR/AR设备
- 自动设备检测和适配
- 优雅的降级处理

### 事件驱动架构
- 基于事件的输入处理
- 支持多种触发条件
- 可配置的阈值和敏感度

## 使用示例

```typescript
// 创建加速度计节点
const accelerometerNode = new AccelerometerNode();
const accelerometerResult = accelerometerNode.execute({
  enable: true,
  sensitivity: 1.0,
  threshold: 0.1
});

// 创建VR控制器节点
const vrControllerNode = new VRControllerInputNode();
const vrControllerResult = vrControllerNode.execute({
  enable: true,
  controllerId: 'right',
  hapticIntensity: 0.5
});

// 创建手势识别节点
const gestureNode = new GestureRecognitionNode();
const gestureResult = gestureNode.execute({
  enable: true,
  sensitivity: 1.0,
  minConfidence: 0.7
});
```

## 输出数据格式

### 位置数据
- **vector2**: `{x: number, y: number}`
- **vector3**: `{x: number, y: number, z: number}`
- **quaternion**: `{x: number, y: number, z: number, w: number}`

### 事件数据
- **event**: 触发时间戳和相关数据
- **boolean**: 状态标志
- **number**: 数值数据

### 复合数据
- **array**: 数组类型数据（如触摸点、手指关节）
- **object**: 复杂对象数据（如手势信息、碰撞对象）

## 测试

运行测试文件来验证节点功能：

```typescript
import { runInputNodeTests } from './test-input-nodes';
runInputNodeTests();
```

## 集成说明

这些输入节点已经集成到DL引擎的可视化脚本系统中，可以通过编辑器的节点面板直接使用。每个节点都提供了完整的输入输出端口定义，支持可视化连接和参数配置。

## 扩展开发

要添加新的输入节点：

1. 继承`VisualScriptNode`基类
2. 实现`setupPorts()`方法定义输入输出端口
3. 实现`execute()`方法处理输入逻辑
4. 添加到相应的导出数组中

## 注意事项

- 所有节点都提供了模拟数据实现，实际使用时需要连接真实的输入设备
- 某些功能需要用户授权（如摄像头、麦克风访问）
- VR/AR功能需要相应的硬件支持
- 建议在实际设备上测试输入功能的准确性和响应性

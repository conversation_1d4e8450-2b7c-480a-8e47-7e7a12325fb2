/**
 * VR/AR高级输入节点集合
 * 提供眼动追踪、手部追踪、语音命令等高级VR/AR输入功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Vector2, Quaternion } from 'three';

/**
 * 眼动追踪输入节点
 * 处理眼动追踪数据
 */
export class EyeTrackingInputNode extends VisualScriptNode {
  public static readonly TYPE = 'input/eye_tracking_input';
  public static readonly NAME = '眼动追踪输入';
  public static readonly DESCRIPTION = '处理眼动追踪数据，包括注视点、眼球运动和眨眼检测';

  constructor(type: string = EyeTrackingInputNode.TYPE, name: string = EyeTrackingInputNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('calibrate', 'event', '校准');
    this.addInput('sensitivity', 'number', '敏感度', 1.0);
    this.addInput('smoothing', 'number', '平滑度', 0.2);

    // 输出端口
    this.addOutput('gazePoint', 'vector2', '注视点');
    this.addOutput('gazeDirection', 'vector3', '注视方向');
    this.addOutput('leftEyePosition', 'vector3', '左眼位置');
    this.addOutput('rightEyePosition', 'vector3', '右眼位置');
    this.addOutput('leftEyeRotation', 'quaternion', '左眼旋转');
    this.addOutput('rightEyeRotation', 'quaternion', '右眼旋转');
    this.addOutput('pupilDiameter', 'number', '瞳孔直径');
    this.addOutput('blinkState', 'boolean', '眨眼状态');
    this.addOutput('trackingQuality', 'number', '追踪质量');
    this.addOutput('onBlink', 'event', '眨眼检测');
    this.addOutput('onGazeChange', 'event', '注视变化');
    this.addOutput('onCalibrationComplete', 'event', '校准完成');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const calibrate = inputs?.calibrate;
      const sensitivity = inputs?.sensitivity as number || 1.0;
      const smoothing = inputs?.smoothing as number || 0.2;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟眼动追踪数据
      const gazePoint = new Vector2(
        (Math.sin(Date.now() * 0.001) + 1) * 0.5 * window.innerWidth,
        (Math.cos(Date.now() * 0.0015) + 1) * 0.5 * window.innerHeight
      );

      const gazeDirection = new Vector3(
        (gazePoint.x / window.innerWidth - 0.5) * 2,
        (0.5 - gazePoint.y / window.innerHeight) * 2,
        -1
      ).normalize();

      const leftEyePosition = new Vector3(-0.03, 1.7, 0);
      const rightEyePosition = new Vector3(0.03, 1.7, 0);

      const eyeRotation = new Quaternion().setFromEuler({
        x: gazeDirection.y * 0.5,
        y: gazeDirection.x * 0.5,
        z: 0
      } as any);

      const pupilDiameter = 3 + Math.sin(Date.now() * 0.002) * 1; // 2-4mm
      const blinkState = Math.random() > 0.98; // 2%概率眨眼
      const trackingQuality = 0.8 + Math.random() * 0.2;

      let onCalibrationComplete = false;
      if (calibrate) {
        onCalibrationComplete = true;
        Debug.log('EyeTrackingInputNode', '眼动追踪校准完成');
      }

      return {
        gazePoint,
        gazeDirection,
        leftEyePosition,
        rightEyePosition,
        leftEyeRotation: eyeRotation,
        rightEyeRotation: eyeRotation,
        pupilDiameter,
        blinkState,
        trackingQuality,
        onBlink: blinkState,
        onGazeChange: true,
        onCalibrationComplete
      };

    } catch (error) {
      Debug.error('EyeTrackingInputNode', '眼动追踪处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      gazePoint: new Vector2(0, 0),
      gazeDirection: new Vector3(0, 0, -1),
      leftEyePosition: new Vector3(0, 0, 0),
      rightEyePosition: new Vector3(0, 0, 0),
      leftEyeRotation: new Quaternion(),
      rightEyeRotation: new Quaternion(),
      pupilDiameter: 3,
      blinkState: false,
      trackingQuality: 0,
      onBlink: false,
      onGazeChange: false,
      onCalibrationComplete: false
    };
  }
}

/**
 * 手部追踪输入节点
 * 处理手部追踪数据
 */
export class HandTrackingInputNode extends VisualScriptNode {
  public static readonly TYPE = 'input/hand_tracking_input';
  public static readonly NAME = '手部追踪输入';
  public static readonly DESCRIPTION = '处理手部追踪数据，包括手部位置、手指关节和手势识别';

  constructor(type: string = HandTrackingInputNode.TYPE, name: string = HandTrackingInputNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('handedness', 'string', '手部选择', 'both');
    this.addInput('trackingMode', 'string', '追踪模式', 'full');
    this.addInput('minConfidence', 'number', '最小置信度', 0.7);

    // 输出端口
    this.addOutput('leftHandPosition', 'vector3', '左手位置');
    this.addOutput('rightHandPosition', 'vector3', '右手位置');
    this.addOutput('leftHandRotation', 'quaternion', '左手旋转');
    this.addOutput('rightHandRotation', 'quaternion', '右手旋转');
    this.addOutput('leftFingerJoints', 'array', '左手关节');
    this.addOutput('rightFingerJoints', 'array', '右手关节');
    this.addOutput('leftHandGesture', 'string', '左手手势');
    this.addOutput('rightHandGesture', 'string', '右手手势');
    this.addOutput('leftHandConfidence', 'number', '左手置信度');
    this.addOutput('rightHandConfidence', 'number', '右手置信度');
    this.addOutput('onHandDetected', 'event', '手部检测');
    this.addOutput('onHandLost', 'event', '手部丢失');
    this.addOutput('onGestureChange', 'event', '手势变化');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const handedness = inputs?.handedness as string || 'both';
      const trackingMode = inputs?.trackingMode as string || 'full';
      const minConfidence = inputs?.minConfidence as number || 0.7;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟手部追踪数据
      const leftHandDetected = handedness !== 'right' && Math.random() > 0.3;
      const rightHandDetected = handedness !== 'left' && Math.random() > 0.3;

      const leftHandPosition = leftHandDetected ? new Vector3(-0.3, 1.2, -0.5) : new Vector3(0, 0, 0);
      const rightHandPosition = rightHandDetected ? new Vector3(0.3, 1.2, -0.5) : new Vector3(0, 0, 0);

      const leftHandRotation = leftHandDetected ? new Quaternion().setFromEuler({
        x: Math.sin(Date.now() * 0.001) * 0.2,
        y: Math.cos(Date.now() * 0.0015) * 0.3,
        z: 0
      } as any) : new Quaternion();

      const rightHandRotation = rightHandDetected ? new Quaternion().setFromEuler({
        x: Math.sin(Date.now() * 0.001) * 0.2,
        y: -Math.cos(Date.now() * 0.0015) * 0.3,
        z: 0
      } as any) : new Quaternion();

      // 生成手指关节数据（每只手21个关节点）
      const leftFingerJoints = leftHandDetected ? this.generateFingerJoints(leftHandPosition) : [];
      const rightFingerJoints = rightHandDetected ? this.generateFingerJoints(rightHandPosition) : [];

      const gestures = ['open', 'fist', 'point', 'thumbs_up', 'peace', 'ok'];
      const leftHandGesture = leftHandDetected ? gestures[Math.floor(Math.random() * gestures.length)] : '';
      const rightHandGesture = rightHandDetected ? gestures[Math.floor(Math.random() * gestures.length)] : '';

      const leftHandConfidence = leftHandDetected ? minConfidence + Math.random() * (1 - minConfidence) : 0;
      const rightHandConfidence = rightHandDetected ? minConfidence + Math.random() * (1 - minConfidence) : 0;

      return {
        leftHandPosition,
        rightHandPosition,
        leftHandRotation,
        rightHandRotation,
        leftFingerJoints,
        rightFingerJoints,
        leftHandGesture,
        rightHandGesture,
        leftHandConfidence,
        rightHandConfidence,
        onHandDetected: leftHandDetected || rightHandDetected,
        onHandLost: !leftHandDetected && !rightHandDetected,
        onGestureChange: Math.random() > 0.8
      };

    } catch (error) {
      Debug.error('HandTrackingInputNode', '手部追踪处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private generateFingerJoints(handPosition: Vector3): Vector3[] {
    const joints = [];
    // 生成21个手指关节点
    for (let i = 0; i < 21; i++) {
      joints.push(new Vector3(
        handPosition.x + (Math.random() - 0.5) * 0.2,
        handPosition.y + (Math.random() - 0.5) * 0.2,
        handPosition.z + (Math.random() - 0.5) * 0.1
      ));
    }
    return joints;
  }

  private getDefaultOutputs(): any {
    return {
      leftHandPosition: new Vector3(0, 0, 0),
      rightHandPosition: new Vector3(0, 0, 0),
      leftHandRotation: new Quaternion(),
      rightHandRotation: new Quaternion(),
      leftFingerJoints: [],
      rightFingerJoints: [],
      leftHandGesture: '',
      rightHandGesture: '',
      leftHandConfidence: 0,
      rightHandConfidence: 0,
      onHandDetected: false,
      onHandLost: false,
      onGestureChange: false
    };
  }
}

/**
 * 语音命令输入节点
 * 处理VR/AR环境中的语音命令
 */
export class VoiceCommandInputNode extends VisualScriptNode {
  public static readonly TYPE = 'input/voice_command_input';
  public static readonly NAME = '语音命令输入';
  public static readonly DESCRIPTION = '处理VR/AR环境中的语音命令，支持自定义命令和语音控制';

  constructor(type: string = VoiceCommandInputNode.TYPE, name: string = VoiceCommandInputNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('language', 'string', '语言', 'zh-CN');
    this.addInput('commands', 'array', '命令列表', []);
    this.addInput('wakeWord', 'string', '唤醒词', '你好');
    this.addInput('timeout', 'number', '超时时间', 5000);
    this.addInput('startListening', 'event', '开始监听');
    this.addInput('stopListening', 'event', '停止监听');

    // 输出端口
    this.addOutput('recognizedText', 'string', '识别文本');
    this.addOutput('matchedCommand', 'string', '匹配命令');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('isListening', 'boolean', '正在监听');
    this.addOutput('isWakeWordDetected', 'boolean', '唤醒词检测');
    this.addOutput('audioLevel', 'number', '音频电平');
    this.addOutput('onCommandRecognized', 'event', '命令识别');
    this.addOutput('onWakeWordDetected', 'event', '唤醒词检测');
    this.addOutput('onListeningStart', 'event', '开始监听');
    this.addOutput('onListeningStop', 'event', '停止监听');
    this.addOutput('onTimeout', 'event', '超时');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const language = inputs?.language as string || 'zh-CN';
      const commands = inputs?.commands as string[] || [];
      const wakeWord = inputs?.wakeWord as string || '你好';
      const timeout = inputs?.timeout as number || 5000;
      const startListening = inputs?.startListening;
      const stopListening = inputs?.stopListening;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟语音命令识别
      let isListening = Math.random() > 0.7;
      let recognizedText = '';
      let matchedCommand = '';
      let confidence = 0;
      let isWakeWordDetected = false;
      let audioLevel = Math.random();

      if (startListening) {
        isListening = true;
      }
      if (stopListening) {
        isListening = false;
      }

      if (isListening && Math.random() > 0.8) {
        const sampleTexts = [wakeWord, '开始', '停止', '确认', '取消', '帮助', '返回'];
        recognizedText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
        confidence = 0.7 + Math.random() * 0.3;

        // 检查唤醒词
        if (recognizedText.includes(wakeWord)) {
          isWakeWordDetected = true;
        }

        // 检查命令匹配
        for (const command of commands) {
          if (recognizedText.includes(command)) {
            matchedCommand = command;
            break;
          }
        }
      }

      return {
        recognizedText,
        matchedCommand,
        confidence,
        isListening,
        isWakeWordDetected,
        audioLevel,
        onCommandRecognized: matchedCommand !== '',
        onWakeWordDetected: isWakeWordDetected,
        onListeningStart: startListening !== undefined,
        onListeningStop: stopListening !== undefined,
        onTimeout: false
      };

    } catch (error) {
      Debug.error('VoiceCommandInputNode', '语音命令处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      recognizedText: '',
      matchedCommand: '',
      confidence: 0,
      isListening: false,
      isWakeWordDetected: false,
      audioLevel: 0,
      onCommandRecognized: false,
      onWakeWordDetected: false,
      onListeningStart: false,
      onListeningStop: false,
      onTimeout: false
    };
  }
}

/**
 * 输入节点测试
 */

// 传感器输入节点
import { 
  AccelerometerNode, 
  GyroscopeNode, 
  CompassNode,
  ProximityNode,
  LightSensorNode,
  PressureSensorNode
} from './SensorInputNodes';

// VR/AR输入节点
import { 
  VRControllerInputNode, 
  VRHeadsetTrackingNode, 
  ARTouchInputNode,
  ARGestureInputNode,
  SpatialInputNode
} from './VRARInputNodes';

// VR/AR高级输入节点
import { 
  EyeTrackingInputNode, 
  HandTrackingInputNode, 
  VoiceCommandInputNode
} from './VRARAdvancedNodes';

// 高级输入节点
import { 
  MultiTouchNode, 
  GestureRecognitionNode, 
  VoiceInputNode,
  MotionSensorNode
} from './AdvancedInputNodes';

/**
 * 测试传感器输入节点
 */
function testSensorInputNodes() {
  console.log('测试传感器输入节点...');
  
  // 测试加速度计节点
  const accelerometerNode = new AccelerometerNode();
  const accelerometerResult = accelerometerNode.execute({
    enable: true,
    sensitivity: 1.0,
    threshold: 0.1
  });
  console.log('加速度计节点结果:', accelerometerResult);
  
  // 测试陀螺仪节点
  const gyroscopeNode = new GyroscopeNode();
  const gyroscopeResult = gyroscopeNode.execute({
    enable: true,
    sensitivity: 1.0
  });
  console.log('陀螺仪节点结果:', gyroscopeResult);
  
  // 测试指南针节点
  const compassNode = new CompassNode();
  const compassResult = compassNode.execute({
    enable: true,
    magneticDeclination: 5.0
  });
  console.log('指南针节点结果:', compassResult);
  
  return true;
}

/**
 * 测试VR/AR输入节点
 */
function testVRARInputNodes() {
  console.log('测试VR/AR输入节点...');
  
  // 测试VR控制器输入节点
  const vrControllerNode = new VRControllerInputNode();
  const vrControllerResult = vrControllerNode.execute({
    enable: true,
    controllerId: 'right',
    hapticIntensity: 0.5
  });
  console.log('VR控制器节点结果:', vrControllerResult);
  
  // 测试VR头显追踪节点
  const vrHeadsetNode = new VRHeadsetTrackingNode();
  const vrHeadsetResult = vrHeadsetNode.execute({
    enable: true,
    smoothing: 0.1
  });
  console.log('VR头显追踪节点结果:', vrHeadsetResult);
  
  // 测试AR触摸输入节点
  const arTouchNode = new ARTouchInputNode();
  const arTouchResult = arTouchNode.execute({
    enable: true,
    raycastDistance: 10,
    touchSensitivity: 1.0
  });
  console.log('AR触摸输入节点结果:', arTouchResult);
  
  return true;
}

/**
 * 测试VR/AR高级输入节点
 */
function testVRARAdvancedNodes() {
  console.log('测试VR/AR高级输入节点...');
  
  // 测试眼动追踪节点
  const eyeTrackingNode = new EyeTrackingInputNode();
  const eyeTrackingResult = eyeTrackingNode.execute({
    enable: true,
    sensitivity: 1.0,
    smoothing: 0.2
  });
  console.log('眼动追踪节点结果:', eyeTrackingResult);
  
  // 测试手部追踪节点
  const handTrackingNode = new HandTrackingInputNode();
  const handTrackingResult = handTrackingNode.execute({
    enable: true,
    handedness: 'both',
    trackingMode: 'full',
    minConfidence: 0.7
  });
  console.log('手部追踪节点结果:', handTrackingResult);
  
  // 测试语音命令节点
  const voiceCommandNode = new VoiceCommandInputNode();
  const voiceCommandResult = voiceCommandNode.execute({
    enable: true,
    language: 'zh-CN',
    commands: ['开始', '停止', '确认'],
    wakeWord: '你好'
  });
  console.log('语音命令节点结果:', voiceCommandResult);
  
  return true;
}

/**
 * 测试高级输入节点
 */
function testAdvancedInputNodes() {
  console.log('测试高级输入节点...');
  
  // 测试多点触摸节点
  const multiTouchNode = new MultiTouchNode();
  const multiTouchResult = multiTouchNode.execute({
    enable: true,
    maxTouches: 10,
    minPressure: 0.1
  });
  console.log('多点触摸节点结果:', multiTouchResult);
  
  // 测试手势识别节点
  const gestureNode = new GestureRecognitionNode();
  const gestureResult = gestureNode.execute({
    enable: true,
    sensitivity: 1.0,
    minConfidence: 0.7
  });
  console.log('手势识别节点结果:', gestureResult);
  
  // 测试语音输入节点
  const voiceInputNode = new VoiceInputNode();
  const voiceInputResult = voiceInputNode.execute({
    enable: true,
    language: 'zh-CN',
    continuous: true
  });
  console.log('语音输入节点结果:', voiceInputResult);
  
  return true;
}

/**
 * 运行所有测试
 */
export function runInputNodeTests() {
  console.log('开始运行输入节点测试...\n');
  
  const tests = [
    { name: '传感器输入节点', test: testSensorInputNodes },
    { name: 'VR/AR输入节点', test: testVRARInputNodes },
    { name: 'VR/AR高级输入节点', test: testVRARAdvancedNodes },
    { name: '高级输入节点', test: testAdvancedInputNodes }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    try {
      const success = test();
      if (success) {
        console.log(`✅ ${name}测试通过\n`);
        passedTests++;
      } else {
        console.log(`❌ ${name}测试失败\n`);
      }
    } catch (error) {
      console.log(`❌ ${name}测试出错:`, error, '\n');
    }
  }
  
  console.log(`测试完成: ${passedTests}/${totalTests} 通过`);
  return passedTests === totalTests;
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runInputNodeTests();
}

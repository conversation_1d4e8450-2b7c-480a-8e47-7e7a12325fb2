/**
 * 材质编辑节点
 * 实现批次3.1的10个材质编辑节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { EventEmitter } from '../../../utils/EventEmitter';
import { Material, MeshStandardMaterial, MeshBasicMaterial, Color, Texture } from 'three';

// 材质编辑管理器
export class MaterialEditingManager extends EventEmitter {
  private static instance: MaterialEditingManager;
  private materials: Map<string, Material> = new Map();
  private materialLibrary: Map<string, Material> = new Map();
  private previewMaterials: Map<string, Material> = new Map();
  private materialVersions: Map<string, Material[]> = new Map();

  public static getInstance(): MaterialEditingManager {
    if (!MaterialEditingManager.instance) {
      MaterialEditingManager.instance = new MaterialEditingManager();
    }
    return MaterialEditingManager.instance;
  }

  /**
   * 创建材质编辑器实例
   */
  public createMaterialEditor(materialId: string, material: Material): any {
    const editor = {
      materialId,
      material,
      properties: this.extractMaterialProperties(material),
      preview: this.createPreviewMaterial(material),
      isDirty: false,
      
      updateProperty: (propertyName: string, value: any) => {
        this.updateMaterialProperty(material, propertyName, value);
        editor.isDirty = true;
        this.emit('materialPropertyChanged', { materialId, propertyName, value });
      },
      
      applyChanges: () => {
        this.applyMaterialChanges(materialId, material);
        editor.isDirty = false;
        this.emit('materialChangesApplied', { materialId, material });
      },
      
      resetChanges: () => {
        this.resetMaterialChanges(materialId, material);
        editor.isDirty = false;
        this.emit('materialChangesReset', { materialId, material });
      }
    };

    this.emit('materialEditorCreated', { materialId, editor });
    return editor;
  }

  /**
   * 创建材质预览
   */
  public createPreviewMaterial(material: Material): Material {
    const preview = material.clone();
    preview.name = `${material.name}_preview`;
    return preview;
  }

  /**
   * 添加材质到库
   */
  public addToLibrary(materialId: string, material: Material): void {
    this.materialLibrary.set(materialId, material.clone());
    this.emit('materialAddedToLibrary', { materialId, material });
  }

  /**
   * 从库中获取材质
   */
  public getFromLibrary(materialId: string): Material | undefined {
    return this.materialLibrary.get(materialId);
  }

  /**
   * 获取材质库列表
   */
  public getLibraryMaterials(): Map<string, Material> {
    return new Map(this.materialLibrary);
  }

  /**
   * 导入材质
   */
  public importMaterial(materialData: any): Material {
    const material = this.createMaterialFromData(materialData);
    this.materials.set(materialData.id, material);
    this.emit('materialImported', { materialId: materialData.id, material });
    return material;
  }

  /**
   * 导出材质
   */
  public exportMaterial(materialId: string): any {
    const material = this.materials.get(materialId);
    if (!material) {
      throw new Error(`材质不存在: ${materialId}`);
    }

    const materialData = this.serializeMaterial(material);
    this.emit('materialExported', { materialId, materialData });
    return materialData;
  }

  /**
   * 验证材质
   */
  public validateMaterial(material: Material): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!material.name) {
      errors.push('材质名称不能为空');
    }

    if (material instanceof MeshStandardMaterial) {
      if (material.metalness < 0 || material.metalness > 1) {
        errors.push('金属度值必须在0-1之间');
      }
      if (material.roughness < 0 || material.roughness > 1) {
        errors.push('粗糙度值必须在0-1之间');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 优化材质
   */
  public optimizeMaterial(material: Material): Material {
    const optimized = material.clone();
    
    // 移除未使用的纹理
    if (optimized instanceof MeshStandardMaterial) {
      if (optimized.map && !this.isTextureUsed(optimized.map)) {
        optimized.map = null;
      }
    }

    this.emit('materialOptimized', { original: material, optimized });
    return optimized;
  }

  /**
   * 创建材质版本
   */
  public createVersion(materialId: string, material: Material): void {
    if (!this.materialVersions.has(materialId)) {
      this.materialVersions.set(materialId, []);
    }
    
    const versions = this.materialVersions.get(materialId)!;
    versions.push(material.clone());
    
    this.emit('materialVersionCreated', { materialId, version: versions.length });
  }

  /**
   * 分析材质使用情况
   */
  public analyzeMaterial(material: Material): any {
    const analysis = {
      name: material.name,
      type: material.type,
      transparent: material.transparent,
      opacity: material.opacity,
      textureCount: 0,
      memoryUsage: 0,
      complexity: 'simple'
    };

    if (material instanceof MeshStandardMaterial) {
      if (material.map) analysis.textureCount++;
      if (material.normalMap) analysis.textureCount++;
      if (material.roughnessMap) analysis.textureCount++;
      if (material.metalnessMap) analysis.textureCount++;
      
      if (analysis.textureCount > 3) {
        analysis.complexity = 'complex';
      } else if (analysis.textureCount > 1) {
        analysis.complexity = 'medium';
      }
    }

    return analysis;
  }

  private extractMaterialProperties(material: Material): any {
    const properties: any = {
      name: material.name,
      transparent: material.transparent,
      opacity: material.opacity,
      visible: material.visible
    };

    if (material instanceof MeshStandardMaterial) {
      properties.color = material.color;
      properties.metalness = material.metalness;
      properties.roughness = material.roughness;
      properties.emissive = material.emissive;
    }

    return properties;
  }

  private updateMaterialProperty(material: Material, propertyName: string, value: any): void {
    if (propertyName in material) {
      (material as any)[propertyName] = value;
      material.needsUpdate = true;
    }
  }

  private applyMaterialChanges(materialId: string, material: Material): void {
    this.materials.set(materialId, material);
    material.needsUpdate = true;
  }

  private resetMaterialChanges(materialId: string, material: Material): void {
    // 重置到原始状态的逻辑
    material.needsUpdate = true;
  }

  private createMaterialFromData(data: any): Material {
    let material: Material;

    switch (data.type) {
      case 'MeshStandardMaterial':
        material = new MeshStandardMaterial();
        break;
      case 'MeshBasicMaterial':
        material = new MeshBasicMaterial();
        break;
      default:
        material = new MeshStandardMaterial();
    }

    // 应用属性
    Object.keys(data.properties || {}).forEach(key => {
      if (key in material) {
        (material as any)[key] = data.properties[key];
      }
    });

    return material;
  }

  private serializeMaterial(material: Material): any {
    return {
      id: material.uuid,
      name: material.name,
      type: material.type,
      properties: this.extractMaterialProperties(material)
    };
  }

  private isTextureUsed(texture: Texture): boolean {
    // 简化的纹理使用检查
    return texture.image !== null;
  }
}

// 全局材质编辑管理器实例
export const globalMaterialEditingManager = MaterialEditingManager.getInstance();

/**
 * 材质编辑器节点
 */
export class MaterialEditorNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialEditor';
  public static readonly NAME = '材质编辑器';
  public static readonly DESCRIPTION = '创建和管理材质编辑器';

  constructor(nodeType: string = MaterialEditorNode.TYPE, name: string = MaterialEditorNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建编辑器');
    this.addInput('material', 'object', '材质对象');
    this.addInput('materialId', 'string', '材质ID');

    // 输出端口
    this.addOutput('editor', 'object', '编辑器对象');
    this.addOutput('material', 'object', '材质对象');
    this.addOutput('properties', 'object', '材质属性');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const material = inputs?.material as Material;
      const materialId = inputs?.materialId as string || material?.uuid || this.generateMaterialId();

      if (!material) {
        throw new Error('未提供材质对象');
      }

      const editor = globalMaterialEditingManager.createMaterialEditor(materialId, material);

      Debug.log('MaterialEditorNode', `材质编辑器创建完成: ${materialId}`);

      return {
        editor,
        material,
        properties: editor.properties,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('MaterialEditorNode', '材质编辑器创建失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      editor: null,
      material: null,
      properties: {},
      onCreated: false,
      onError: false
    };
  }

  private generateMaterialId(): string {
    return `material_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 材质预览节点
 */
export class MaterialPreviewNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialPreview';
  public static readonly NAME = '材质预览';
  public static readonly DESCRIPTION = '预览材质效果';

  constructor(nodeType: string = MaterialPreviewNode.TYPE, name: string = MaterialPreviewNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('preview', 'trigger', '生成预览');
    this.addInput('material', 'object', '材质对象');
    this.addInput('geometry', 'string', '预览几何体');
    this.addInput('environment', 'string', '环境设置');
    this.addInput('lighting', 'string', '光照设置');

    // 输出端口
    this.addOutput('previewMaterial', 'object', '预览材质');
    this.addOutput('previewMesh', 'object', '预览网格');
    this.addOutput('previewImage', 'string', '预览图像');
    this.addOutput('onPreviewed', 'trigger', '预览完成');
    this.addOutput('onError', 'trigger', '预览失败');
  }

  public execute(inputs?: any): any {
    try {
      const previewTrigger = inputs?.preview;
      if (!previewTrigger) {
        return this.getDefaultOutputs();
      }

      const material = inputs?.material as Material;
      const geometry = inputs?.geometry as string || 'sphere';
      const environment = inputs?.environment as string || 'studio';
      const lighting = inputs?.lighting as string || 'default';

      if (!material) {
        throw new Error('未提供材质对象');
      }

      const previewMaterial = globalMaterialEditingManager.createPreviewMaterial(material);

      // 创建预览网格（简化实现）
      const previewMesh = {
        material: previewMaterial,
        geometry: geometry,
        environment: environment,
        lighting: lighting
      };

      Debug.log('MaterialPreviewNode', `材质预览生成完成: ${material.name || material.uuid}`);

      return {
        previewMaterial,
        previewMesh,
        previewImage: '', // 实际实现中应该生成预览图像
        onPreviewed: true,
        onError: false
      };

    } catch (error) {
      Debug.error('MaterialPreviewNode', '材质预览生成失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      previewMaterial: null,
      previewMesh: null,
      previewImage: '',
      onPreviewed: false,
      onError: false
    };
  }
}

/**
 * 材质库节点
 */
export class MaterialLibraryNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialLibrary';
  public static readonly NAME = '材质库';
  public static readonly DESCRIPTION = '管理材质库';

  constructor(nodeType: string = MaterialLibraryNode.TYPE, name: string = MaterialLibraryNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('add', 'trigger', '添加到库');
    this.addInput('remove', 'trigger', '从库移除');
    this.addInput('get', 'trigger', '从库获取');
    this.addInput('list', 'trigger', '列出所有');
    this.addInput('material', 'object', '材质对象');
    this.addInput('materialId', 'string', '材质ID');

    // 输出端口
    this.addOutput('material', 'object', '材质对象');
    this.addOutput('materials', 'array', '材质列表');
    this.addOutput('materialIds', 'array', '材质ID列表');
    this.addOutput('count', 'number', '材质数量');
    this.addOutput('onAdded', 'trigger', '添加完成');
    this.addOutput('onRemoved', 'trigger', '移除完成');
    this.addOutput('onGot', 'trigger', '获取完成');
    this.addOutput('onListed', 'trigger', '列出完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const addTrigger = inputs?.add;
      const removeTrigger = inputs?.remove;
      const getTrigger = inputs?.get;
      const listTrigger = inputs?.list;
      const material = inputs?.material as Material;
      const materialId = inputs?.materialId as string;

      if (addTrigger) {
        if (!material || !materialId) {
          throw new Error('添加材质需要提供材质对象和ID');
        }

        globalMaterialEditingManager.addToLibrary(materialId, material);

        Debug.log('MaterialLibraryNode', `材质添加到库: ${materialId}`);

        return {
          material,
          materials: [],
          materialIds: [],
          count: 0,
          onAdded: true,
          onRemoved: false,
          onGot: false,
          onListed: false,
          onError: false
        };
      }

      if (getTrigger) {
        if (!materialId) {
          throw new Error('获取材质需要提供材质ID');
        }

        const retrievedMaterial = globalMaterialEditingManager.getFromLibrary(materialId);

        Debug.log('MaterialLibraryNode', `从库获取材质: ${materialId}`);

        return {
          material: retrievedMaterial || null,
          materials: [],
          materialIds: [],
          count: 0,
          onAdded: false,
          onRemoved: false,
          onGot: true,
          onListed: false,
          onError: !retrievedMaterial
        };
      }

      if (listTrigger) {
        const libraryMaterials = globalMaterialEditingManager.getLibraryMaterials();
        const materials = Array.from(libraryMaterials.values());
        const materialIds = Array.from(libraryMaterials.keys());

        Debug.log('MaterialLibraryNode', `材质库列出: ${materials.length} 个材质`);

        return {
          material: null,
          materials,
          materialIds,
          count: materials.length,
          onAdded: false,
          onRemoved: false,
          onGot: false,
          onListed: true,
          onError: false
        };
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('MaterialLibraryNode', '材质库操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      materials: [],
      materialIds: [],
      count: 0,
      onAdded: false,
      onRemoved: false,
      onGot: false,
      onListed: false,
      onError: false
    };
  }
}

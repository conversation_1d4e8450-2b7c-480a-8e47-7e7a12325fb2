/**
 * 通知服务节点集合
 * 批次2.1 - 服务器集成节点
 * 提供邮件通知、推送通知、短信通知等专业通知服务功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 通知管理器
 */
class NotificationManager {
  private notifications: Map<string, any> = new Map();
  private templates: Map<string, string> = new Map();
  private providers: Map<string, any> = new Map();

  constructor() {
    this.initializeProviders();
    this.initializeTemplates();
  }

  private initializeProviders(): void {
    // 初始化通知提供商
    this.providers.set('email', {
      name: 'Email Provider',
      enabled: true,
      config: {
        smtp: {
          host: 'localhost',
          port: 587,
          secure: false
        }
      }
    });

    this.providers.set('push', {
      name: 'Push Notification Provider',
      enabled: true,
      config: {
        fcm: {
          serverKey: 'your-server-key'
        }
      }
    });

    this.providers.set('sms', {
      name: 'SMS Provider',
      enabled: true,
      config: {
        twilio: {
          accountSid: 'your-account-sid',
          authToken: 'your-auth-token'
        }
      }
    });
  }

  private initializeTemplates(): void {
    // 初始化通知模板
    this.templates.set('welcome', '欢迎使用我们的服务！');
    this.templates.set('verification', '您的验证码是：{code}');
    this.templates.set('notification', '您有新的通知：{message}');
  }

  /**
   * 发送通知
   */
  sendNotification(type: string, recipient: string, message: string, options: any = {}): any {
    const notificationId = this.generateId();
    const notification = {
      id: notificationId,
      type,
      recipient,
      message,
      options,
      status: 'pending',
      createdAt: new Date(),
      sentAt: null
    };

    // 模拟发送过程
    setTimeout(() => {
      notification.status = 'sent';
      notification.sentAt = new Date();
    }, 100);

    this.notifications.set(notificationId, notification);
    return notification;
  }

  /**
   * 获取通知状态
   */
  getNotificationStatus(notificationId: string): any {
    return this.notifications.get(notificationId);
  }

  /**
   * 获取模板
   */
  public getTemplate(templateId: string): string | undefined {
    return this.templates.get(templateId);
  }

  /**
   * 设置模板
   */
  public setTemplate(templateId: string, template: string): void {
    this.templates.set(templateId, template);
  }

  /**
   * 获取提供商
   */
  public getProvider(providerId: string): any {
    return this.providers.get(providerId);
  }

  /**
   * 添加通知
   */
  public addNotification(notification: any): string {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.notifications.set(id, {
      ...notification,
      id,
      createdAt: new Date().toISOString(),
      status: 'pending'
    });
    return id;
  }

  /**
   * 获取通知
   */
  public getNotification(id: string): any {
    return this.notifications.get(id);
  }

  /**
   * 更新通知状态
   */
  public updateNotificationStatus(id: string, status: string): void {
    const notification = this.notifications.get(id);
    if (notification) {
      notification.status = status;
      notification.updatedAt = new Date().toISOString();
    }
  }

  /**
   * 获取通知列表
   */
  public getNotifications(filter?: any): any[] {
    const notifications = Array.from(this.notifications.values());

    if (!filter) {
      return notifications;
    }

    return notifications.filter(notification => {
      if (filter.status && notification.status !== filter.status) {
        return false;
      }
      if (filter.type && notification.type !== filter.type) {
        return false;
      }
      if (filter.userId && notification.userId !== filter.userId) {
        return false;
      }
      return true;
    });
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 通知服务基础节点
 */
export abstract class NotificationServiceNode extends VisualScriptNode {
  protected static notificationManager: NotificationManager = new NotificationManager();

  protected getDefaultOutputs(): any {
    return {
      success: false,
      messageId: '',
      status: 'failed',
      error: '',
      deliveryTime: 0,
      onSent: false,
      onDelivered: false,
      onFailed: false
    };
  }

  protected getSuccessOutputs(messageId: string, status: string = 'sent', deliveryTime: number = 0): any {
    return {
      success: true,
      messageId,
      status,
      error: '',
      deliveryTime,
      onSent: status === 'sent',
      onDelivered: status === 'delivered',
      onFailed: false
    };
  }

  protected getErrorOutputs(error: string, messageId: string = ''): any {
    return {
      success: false,
      messageId,
      status: 'failed',
      error,
      deliveryTime: 0,
      onSent: false,
      onDelivered: false,
      onFailed: true
    };
  }
}

/**
 * 邮件通知节点
 * 批次2.1 - 通知服务节点
 */
export class EmailNotificationNode extends NotificationServiceNode {
  public static readonly TYPE = 'EmailNotification';
  public static readonly NAME = '邮件通知';
  public static readonly DESCRIPTION = '发送邮件通知消息';

  constructor(nodeType: string = EmailNotificationNode.TYPE, name: string = EmailNotificationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发发送');
    this.addInput('to', 'string', '收件人邮箱');
    this.addInput('cc', 'string', '抄送邮箱');
    this.addInput('bcc', 'string', '密送邮箱');
    this.addInput('subject', 'string', '邮件主题');
    this.addInput('content', 'string', '邮件内容');
    this.addInput('contentType', 'string', '内容类型');
    this.addInput('attachments', 'array', '附件列表');
    this.addInput('priority', 'string', '优先级');
    this.addInput('template', 'string', '邮件模板');
    this.addInput('templateData', 'object', '模板数据');

    // 输出端口
    this.addOutput('success', 'boolean', '发送成功');
    this.addOutput('messageId', 'string', '消息ID');
    this.addOutput('status', 'string', '发送状态');
    this.addOutput('error', 'string', '错误信息');
    this.addOutput('deliveryTime', 'number', '发送耗时');
    this.addOutput('onSent', 'trigger', '发送成功事件');
    this.addOutput('onDelivered', 'trigger', '投递成功事件');
    this.addOutput('onFailed', 'trigger', '发送失败事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const to = inputs?.to as string;
      const cc = inputs?.cc as string;
      const bcc = inputs?.bcc as string;
      const subject = inputs?.subject as string;
      const content = inputs?.content as string;
      const contentType = inputs?.contentType as string || 'text/html';
      const attachments = inputs?.attachments as any[] || [];
      const priority = inputs?.priority as string || 'normal';
      const template = inputs?.template as string;
      const templateData = inputs?.templateData || {};

      if (!to || !subject) {
        return this.getErrorOutputs('收件人和主题不能为空');
      }

      // 发送邮件
      const result = this.sendEmail({
        to, cc, bcc, subject, content, contentType,
        attachments, priority, template, templateData
      });

      Debug.log('EmailNotificationNode', `邮件发送${result.success ? '成功' : '失败'}: ${to}`);

      return result;
    } catch (error) {
      Debug.error('EmailNotificationNode', '邮件发送失败', error);
      return this.getErrorOutputs(error instanceof Error ? error.message : '邮件发送失败');
    }
  }

  private sendEmail(params: any): any {
    const startTime = Date.now();
    
    try {
      // 模拟邮件发送
      const messageId = `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 验证邮箱格式
      if (!this.isValidEmail(params.to)) {
        return this.getErrorOutputs('无效的邮箱地址');
      }

      // 处理模板
      let finalContent = params.content;
      if (params.template && params.templateData) {
        finalContent = this.processTemplate(params.template, params.templateData);
      }

      // 模拟发送延迟
      const deliveryTime = Date.now() - startTime + Math.random() * 1000;

      // 模拟发送结果
      const success = Math.random() > 0.1; // 90% 成功率

      if (success) {
        return this.getSuccessOutputs(messageId, 'sent', deliveryTime);
      } else {
        return this.getErrorOutputs('邮件服务器连接失败', messageId);
      }
    } catch (error) {
      const deliveryTime = Date.now() - startTime;
      return this.getErrorOutputs(error instanceof Error ? error.message : '发送失败', '');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private processTemplate(template: string, data: any): string {
    let result = template;
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    }
    return result;
  }
}



/**
 * 推送通知节点
 * 批次2.1 - 通知服务节点
 */
export class PushNotificationNode extends NotificationServiceNode {
  public static readonly TYPE = 'PushNotification';
  public static readonly NAME = '推送通知';
  public static readonly DESCRIPTION = '发送移动设备推送通知';

  constructor(nodeType: string = PushNotificationNode.TYPE, name: string = PushNotificationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发发送');
    this.addInput('deviceToken', 'string', '设备令牌');
    this.addInput('deviceTokens', 'array', '设备令牌列表');
    this.addInput('title', 'string', '通知标题');
    this.addInput('body', 'string', '通知内容');
    this.addInput('icon', 'string', '通知图标');
    this.addInput('image', 'string', '通知图片');
    this.addInput('sound', 'string', '通知声音');
    this.addInput('badge', 'number', '角标数量');
    this.addInput('data', 'object', '自定义数据');
    this.addInput('clickAction', 'string', '点击动作');
    this.addInput('priority', 'string', '优先级');
    this.addInput('timeToLive', 'number', '生存时间');

    // 输出端口
    this.addOutput('success', 'boolean', '发送成功');
    this.addOutput('messageId', 'string', '消息ID');
    this.addOutput('status', 'string', '发送状态');
    this.addOutput('error', 'string', '错误信息');
    this.addOutput('deliveryTime', 'number', '发送耗时');
    this.addOutput('successCount', 'number', '成功数量');
    this.addOutput('failureCount', 'number', '失败数量');
    this.addOutput('onSent', 'trigger', '发送成功事件');
    this.addOutput('onDelivered', 'trigger', '投递成功事件');
    this.addOutput('onFailed', 'trigger', '发送失败事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const deviceToken = inputs?.deviceToken as string;
      const deviceTokens = inputs?.deviceTokens as string[] || [];
      const title = inputs?.title as string;
      const body = inputs?.body as string;
      const icon = inputs?.icon as string;
      const image = inputs?.image as string;
      const sound = inputs?.sound as string || 'default';
      const badge = inputs?.badge as number;
      const data = inputs?.data || {};
      const clickAction = inputs?.clickAction as string;
      const priority = inputs?.priority as string || 'normal';
      const timeToLive = inputs?.timeToLive as number || 3600;

      if (!title || !body) {
        return this.getErrorOutputs('标题和内容不能为空');
      }

      // 准备设备令牌列表
      const tokens = deviceToken ? [deviceToken] : deviceTokens;
      if (tokens.length === 0) {
        return this.getErrorOutputs('设备令牌不能为空');
      }

      // 发送推送通知
      const result = this.sendPushNotification({
        tokens, title, body, icon, image, sound, badge,
        data, clickAction, priority, timeToLive
      });

      Debug.log('PushNotificationNode', `推送通知发送${result.success ? '成功' : '失败'}: ${tokens.length} 个设备`);

      return result;
    } catch (error) {
      Debug.error('PushNotificationNode', '推送通知发送失败', error);
      return this.getErrorOutputs(error instanceof Error ? error.message : '推送通知发送失败');
    }
  }

  private sendPushNotification(params: any): any {
    const startTime = Date.now();

    try {
      const messageId = `push_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 验证设备令牌
      const validTokens = params.tokens.filter((token: string) => this.isValidDeviceToken(token));
      if (validTokens.length === 0) {
        return this.getErrorOutputs('没有有效的设备令牌');
      }

      // 构建推送消息
      const message = {
        notification: {
          title: params.title,
          body: params.body,
          icon: params.icon,
          image: params.image,
          sound: params.sound,
          badge: params.badge,
          click_action: params.clickAction
        },
        data: params.data,
        android: {
          priority: params.priority,
          ttl: params.timeToLive * 1000
        },
        apns: {
          headers: {
            'apns-priority': params.priority === 'high' ? '10' : '5',
            'apns-expiration': String(Math.floor(Date.now() / 1000) + params.timeToLive)
          }
        }
      };

      // 模拟发送延迟
      const deliveryTime = Date.now() - startTime + Math.random() * 2000;

      // 模拟发送结果
      const successRate = 0.85; // 85% 成功率
      const successCount = Math.floor(validTokens.length * successRate);
      const failureCount = validTokens.length - successCount;

      const success = successCount > 0;

      return {
        success,
        messageId,
        status: success ? 'sent' : 'failed',
        error: success ? '' : '部分设备发送失败',
        deliveryTime,
        successCount,
        failureCount,
        onSent: success,
        onDelivered: false,
        onFailed: !success
      };
    } catch (error) {
      const deliveryTime = Date.now() - startTime;
      return {
        ...this.getErrorOutputs(error instanceof Error ? error.message : '发送失败'),
        successCount: 0,
        failureCount: params.tokens.length
      };
    }
  }

  private isValidDeviceToken(token: string): boolean {
    // 简化的设备令牌验证
    return token && token.length >= 32 && /^[a-zA-Z0-9_-]+$/.test(token);
  }
}

/**
 * 短信通知节点
 * 批次2.1 - 通知服务节点
 */
export class SMSNotificationNode extends NotificationServiceNode {
  public static readonly TYPE = 'SMSNotification';
  public static readonly NAME = '短信通知';
  public static readonly DESCRIPTION = '发送短信通知消息';

  constructor(nodeType: string = SMSNotificationNode.TYPE, name: string = SMSNotificationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发发送');
    this.addInput('phoneNumber', 'string', '手机号码');
    this.addInput('phoneNumbers', 'array', '手机号码列表');
    this.addInput('message', 'string', '短信内容');
    this.addInput('template', 'string', '短信模板');
    this.addInput('templateParams', 'object', '模板参数');
    this.addInput('signature', 'string', '短信签名');
    this.addInput('priority', 'string', '优先级');
    this.addInput('sendTime', 'string', '定时发送时间');

    // 输出端口
    this.addOutput('success', 'boolean', '发送成功');
    this.addOutput('messageId', 'string', '消息ID');
    this.addOutput('status', 'string', '发送状态');
    this.addOutput('error', 'string', '错误信息');
    this.addOutput('deliveryTime', 'number', '发送耗时');
    this.addOutput('successCount', 'number', '成功数量');
    this.addOutput('failureCount', 'number', '失败数量');
    this.addOutput('cost', 'number', '发送费用');
    this.addOutput('onSent', 'trigger', '发送成功事件');
    this.addOutput('onDelivered', 'trigger', '投递成功事件');
    this.addOutput('onFailed', 'trigger', '发送失败事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const phoneNumber = inputs?.phoneNumber as string;
      const phoneNumbers = inputs?.phoneNumbers as string[] || [];
      const message = inputs?.message as string;
      const template = inputs?.template as string;
      const templateParams = inputs?.templateParams || {};
      const signature = inputs?.signature as string || '【系统通知】';
      const priority = inputs?.priority as string || 'normal';
      const sendTime = inputs?.sendTime as string;

      // 准备手机号码列表
      const phones = phoneNumber ? [phoneNumber] : phoneNumbers;
      if (phones.length === 0) {
        return this.getErrorOutputs('手机号码不能为空');
      }

      // 准备短信内容
      let finalMessage = message;
      if (template && templateParams) {
        finalMessage = this.processTemplate(template, templateParams);
      }

      if (!finalMessage) {
        return this.getErrorOutputs('短信内容不能为空');
      }

      // 发送短信
      const result = this.sendSMS({
        phones, message: finalMessage, signature, priority, sendTime
      });

      Debug.log('SMSNotificationNode', `短信发送${result.success ? '成功' : '失败'}: ${phones.length} 个号码`);

      return result;
    } catch (error) {
      Debug.error('SMSNotificationNode', '短信发送失败', error);
      return this.getErrorOutputs(error instanceof Error ? error.message : '短信发送失败');
    }
  }

  private sendSMS(params: any): any {
    const startTime = Date.now();

    try {
      const messageId = `sms_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 验证手机号码
      const validPhones = params.phones.filter((phone: string) => this.isValidPhoneNumber(phone));
      if (validPhones.length === 0) {
        return this.getErrorOutputs('没有有效的手机号码');
      }

      // 检查短信长度
      const messageLength = params.message.length;
      if (messageLength > 500) {
        return this.getErrorOutputs('短信内容过长，最多500字符');
      }

      // 计算费用（按条数计算）
      const smsCount = Math.ceil(messageLength / 70); // 每70字符一条短信
      const costPerSMS = 0.05; // 每条短信5分钱
      const totalCost = validPhones.length * smsCount * costPerSMS;

      // 模拟发送延迟
      const deliveryTime = Date.now() - startTime + Math.random() * 3000;

      // 模拟发送结果
      const successRate = 0.95; // 95% 成功率
      const successCount = Math.floor(validPhones.length * successRate);
      const failureCount = validPhones.length - successCount;

      const success = successCount > 0;

      return {
        success,
        messageId,
        status: success ? 'sent' : 'failed',
        error: success ? '' : '部分号码发送失败',
        deliveryTime,
        successCount,
        failureCount,
        cost: totalCost,
        onSent: success,
        onDelivered: false,
        onFailed: !success
      };
    } catch (error) {
      const deliveryTime = Date.now() - startTime;
      return {
        ...this.getErrorOutputs(error instanceof Error ? error.message : '发送失败'),
        successCount: 0,
        failureCount: params.phones.length,
        cost: 0
      };
    }
  }

  private isValidPhoneNumber(phone: string): boolean {
    // 简化的手机号码验证（支持中国大陆手机号）
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  }

  private processTemplate(template: string, params: any): string {
    let result = template;
    for (const [key, value] of Object.entries(params)) {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    }
    return result;
  }
}

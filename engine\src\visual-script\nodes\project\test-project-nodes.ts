/**
 * 项目管理节点测试
 */

import { 
  CreateProjectNode, 
  LoadProjectNode, 
  SaveProjectNode,
  ProjectVersionNode,
  ProjectCollaborationNode,
  ProjectPermissionNode,
  ProjectBackupNode,
  ProjectAnalyticsNode,
  ProjectTemplateNode,
  ProjectExportNode
} from './ProjectManagementNodes';

/**
 * 测试创建项目节点
 */
function testCreateProjectNode() {
  console.log('测试创建项目节点...');
  
  const node = new CreateProjectNode();
  const inputs = {
    projectName: '测试项目',
    description: '这是一个测试项目',
    author: '测试用户',
    tags: ['测试', '项目管理'],
    metadata: { version: '1.0.0' }
  };
  
  const result = node.execute(inputs);
  console.log('创建项目结果:', result);
  
  return result.success;
}

/**
 * 测试加载项目节点
 */
function testLoadProjectNode() {
  console.log('测试加载项目节点...');
  
  const node = new LoadProjectNode();
  const inputs = {
    projectId: 'test-project-001',
    version: 'latest'
  };
  
  const result = node.execute(inputs);
  console.log('加载项目结果:', result);
  
  return result.success;
}

/**
 * 测试保存项目节点
 */
function testSaveProjectNode() {
  console.log('测试保存项目节点...');
  
  const node = new SaveProjectNode();
  const inputs = {
    projectId: 'test-project-001',
    projectData: { scenes: [], assets: [] },
    autoVersion: true,
    versionDescription: '测试保存'
  };
  
  const result = node.execute(inputs);
  console.log('保存项目结果:', result);
  
  return result.success;
}

/**
 * 测试项目版本节点
 */
function testProjectVersionNode() {
  console.log('测试项目版本节点...');
  
  const node = new ProjectVersionNode();
  const inputs = {
    projectId: 'test-project-001',
    action: 'list'
  };
  
  const result = node.execute(inputs);
  console.log('项目版本结果:', result);
  
  return result.success;
}

/**
 * 测试项目协作节点
 */
function testProjectCollaborationNode() {
  console.log('测试项目协作节点...');
  
  const node = new ProjectCollaborationNode();
  const inputs = {
    projectId: 'test-project-001',
    action: 'getCollaborators'
  };
  
  const result = node.execute(inputs);
  console.log('项目协作结果:', result);
  
  return result.success;
}

/**
 * 测试项目权限节点
 */
function testProjectPermissionNode() {
  console.log('测试项目权限节点...');
  
  const node = new ProjectPermissionNode();
  const inputs = {
    projectId: 'test-project-001',
    userId: 'user-123',
    action: 'check',
    permission: 'read'
  };
  
  const result = node.execute(inputs);
  console.log('项目权限结果:', result);
  
  return result.success;
}

/**
 * 测试项目备份节点
 */
function testProjectBackupNode() {
  console.log('测试项目备份节点...');
  
  const node = new ProjectBackupNode();
  const inputs = {
    projectId: 'test-project-001',
    action: 'create',
    description: '测试备份',
    includeAssets: true
  };
  
  const result = node.execute(inputs);
  console.log('项目备份结果:', result);
  
  return result.success;
}

/**
 * 测试项目分析节点
 */
function testProjectAnalyticsNode() {
  console.log('测试项目分析节点...');
  
  const node = new ProjectAnalyticsNode();
  const inputs = {
    projectId: 'test-project-001',
    metricType: 'overview',
    timeRange: '7d',
    includeDetails: false
  };
  
  const result = node.execute(inputs);
  console.log('项目分析结果:', result);
  
  return result.success;
}

/**
 * 测试项目模板节点
 */
function testProjectTemplateNode() {
  console.log('测试项目模板节点...');
  
  const node = new ProjectTemplateNode();
  const inputs = {
    action: 'list',
    category: 'general'
  };
  
  const result = node.execute(inputs);
  console.log('项目模板结果:', result);
  
  return result.success;
}

/**
 * 测试项目导出节点
 */
function testProjectExportNode() {
  console.log('测试项目导出节点...');
  
  const node = new ProjectExportNode();
  const inputs = {
    projectId: 'test-project-001',
    format: 'json',
    includeAssets: true,
    includeMetadata: true,
    compressionLevel: 5
  };
  
  const result = node.execute(inputs);
  console.log('项目导出结果:', result);
  
  return result.success;
}

/**
 * 运行所有测试
 */
export function runProjectNodeTests() {
  console.log('开始运行项目管理节点测试...\n');
  
  const tests = [
    { name: '创建项目', test: testCreateProjectNode },
    { name: '加载项目', test: testLoadProjectNode },
    { name: '保存项目', test: testSaveProjectNode },
    { name: '项目版本', test: testProjectVersionNode },
    { name: '项目协作', test: testProjectCollaborationNode },
    { name: '项目权限', test: testProjectPermissionNode },
    { name: '项目备份', test: testProjectBackupNode },
    { name: '项目分析', test: testProjectAnalyticsNode },
    { name: '项目模板', test: testProjectTemplateNode },
    { name: '项目导出', test: testProjectExportNode }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    try {
      const success = test();
      if (success) {
        console.log(`✅ ${name}测试通过\n`);
        passedTests++;
      } else {
        console.log(`❌ ${name}测试失败\n`);
      }
    } catch (error) {
      console.log(`❌ ${name}测试出错:`, error, '\n');
    }
  }
  
  console.log(`测试完成: ${passedTests}/${totalTests} 通过`);
  return passedTests === totalTests;
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runProjectNodeTests();
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# 读取文件
with open('engine/src/visual-script/nodes/industrial/PredictiveMaintenanceNodes.ts', 'r', encoding='utf-8') as f:
    content = f.read()

# 定义替换映射
replacements = [
    ('批准工作流步�?', '批准工作流步骤'),
    ('拒绝工作流步�?', '拒绝工作流步骤'),
    ('完成工作�?', '完成工作流'),
    ('取消工作�?', '取消工作流'),
    ('获取工作流状�?', '获取工作流状态'),
    ('预计故障时间(�?', '预计故障时间(天)'),
    ('预测置信�?', '预测置信度'),
    ('高风险预�?', '高风险预警'),
    ('确定故障类型和严重程�?', '确定故障类型和严重程度'),
    ('传感器故�?', '传感器故障'),
    ('预测设备可能�?', '预测设备可能在'),
    ('电压不稳�?', '电压不稳定'),
    ('版本兼容�?', '版本兼容性'),
    ('安排专业检�?', '安排专业检查'),
    ('立即停机检�?', '立即停机检查'),
    ('联系技术支�?', '联系技术支持'),
    ('预防性维�?', '预防性维护'),
    ('优先�?', '优先级'),
    ('基于预测的维护调�?', '基于预测的维护调度'),
    ('资源冲突检�?', '资源冲突检查'),
    ('紧急程�?', '紧急程度'),
    ('供应�?', '供应商'),
    ('零件可用�?', '零件可用性'),
    ('总成�?', '总成本'),
    ('零件可用性检�?', '零件可用性检查'),
    ('管理和查询设备维护历史记�?', '管理和查询设备维护历史记录'),
    ('分析和管理维护成�?', '分析和管理维护成本'),
    ('维护成本计算: ${deviceId} - 总成�?', '维护成本计算: ${deviceId} - 总成本'),
    ('维护成本优化: ${deviceId} - 优化后成�?', '维护成本优化: ${deviceId} - 优化后成本'),
    ('基准�?', '基准值'),
    ('维护有效�?', '维护有效性'),
    ('设备可靠�?', '设备可靠性'),
    ('维护有效性分�?', '维护有效性分析'),
    ('有效�?', '有效性'),
    ('设备可靠性分�?', '设备可靠性分析'),
    ('可靠�?', '可靠性'),
    ('优化维护策略和计�?', '优化维护策略和计划'),
    ('生成维护报告和文�?', '生成维护报告和文档'),
    ('接收�?', '接收者'),
    ('报告发�?', '报告发送'),
    ('维护工作流节�?', '维护工作流节点'),
    ('维护工作�?', '维护工作流'),
    ('管理维护工作流程和审�?', '管理维护工作流程和审批'),
    ('工作流动�?', '工作流动作'),
    ('工作流类�?', '工作流类型'),
    ('指派�?', '指派者'),
    ('工作�?', '工作流'),
    ('进度百分�?', '进度百分比'),
    ('工作流状�?', '工作流状态'),
    ('工作流开�?', '工作流开始'),
    ('工作流完�?', '工作流完成'),
    ('工作流错�?', '工作流错误'),
    ('不支持的工作流动�?', '不支持的工作流动作'),
    ('维护工作流操作失�?', '维护工作流操作失败'),
    ('维护工作流开�?', '维护工作流开始'),
    ('工作流步骤批�?', '工作流步骤批准'),
    ('工作流步骤拒�?', '工作流步骤拒绝'),
    ('工作流完�?', '工作流完成'),
    ('工作流取�?', '工作流取消'),
    ('工作流状态查�?', '工作流状态查询'),
]

# 应用替换
for old, new in replacements:
    content = content.replace(old, new)

# 写回文件
with open('engine/src/visual-script/nodes/industrial/PredictiveMaintenanceNodes.ts', 'w', encoding='utf-8') as f:
    f.write(content)

print("修复完成！")
